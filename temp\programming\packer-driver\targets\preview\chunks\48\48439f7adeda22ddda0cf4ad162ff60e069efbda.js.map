{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts"], "names": ["BulletProperty", "_decorator", "Sprite", "Color", "Prefab", "EDITOR", "ObjectPool", "Movable", "eSpriteDefaultFacing", "eMoveEvent", "BulletSystem", "EventGroupContext", "PropertyContainer", "FCollider", "ColliderGroupType", "FBoxCollider", "Entity", "eEntityTag", "log<PERSON>arn", "BulletSourceType", "BulletType", "ccclass", "property", "executeInEditMode", "constructor", "duration", "delayDestroy", "attack", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "defaultFacing", "isDestroyOutScreen", "isDestructive", "isDestructiveOnHit", "isFacingMoveDir", "isTrackingTarget", "bulletConfig", "undefined", "addProperty", "WHITE", "Up", "resetFromData", "data", "value", "eval", "copyFrom", "other", "forEachProperty", "k", "prop", "getPropertyValue", "clear", "Bullet", "type", "displayName", "isAlive", "elapsedTime", "emitter", "bulletData", "eventGroups", "sourceType", "source", "MAINPLANE", "NORMAL", "onLoad", "mover", "getComponent", "addComponent", "on", "onBecomeInvisible", "onDestroyBullet", "collider", "boxCollider", "node", "setScale", "bulletSprite", "onCreate", "ent", "getEntity", "isShootFromEnemy", "ENEMYPLANE", "initBaseData", "groupType", "BULLET_ENEMY", "BULLET_SELF", "isEnable", "addTag", "EnemyBullet", "<PERSON><PERSON><PERSON><PERSON>", "resetProperties", "onReady", "notifyAll", "setMovable", "resetEventGroups", "destroySelf", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "returnNode", "bulletProp", "eventGroupData", "length", "ctx", "bullet", "eventName", "createBulletEventGroup", "tick", "dt", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "group", "tryStop", "removeAllComp", "clearTags", "scheduleOnce", "onCollide", "remove", "getAttack"], "mappings": ";;;4UAmBaA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnBJC,MAAAA,U,OAAAA,U;AAAmCC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAClDC,MAAAA,M,UAAAA,M;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,U,iBAAAA,U;;AAC/BC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,iB,iBAAAA,iB;;AAGZC,MAAAA,S;AAAaC,MAAAA,iB,iBAAAA,iB;;AACbC,MAAAA,Y;;AACAC,MAAAA,M;;AACEC,MAAAA,U,kBAAAA,U;;AACQC,MAAAA,O,kBAAAA,O;;AACGC,MAAAA,gB,kBAAAA,gB;AAAkBC,MAAAA,U,kBAAAA,U;;;;;;;;;OAEhC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CtB,U;;gCAEpCD,c,GAAN,MAAMA,cAAN;AAAA;AAAA,kDAAuD;AAqB1DwB,QAAAA,WAAW,GAAG;AACV;AADU,eApBPC,QAoBO;AApBqC;AAoBrC,eAnBPC,YAmBO;AAnBqC;AAmBrC,eAjBPC,MAiBO;AAjBqC;AAiBrC,eAhBPC,KAgBO;AAhBqC;AAgBrC,eAfPC,UAeO;AAfqC;AAerC,eAdPC,YAcO;AAdqC;AAcrC,eAbPC,iBAaO;AAbqC;AAarC,eAZPC,KAYO;AAZqC;AAYrC,eAXPC,KAWO;AAXqC;AAWrC,eAVPC,aAUO;AAVkD;AAUlD,eARPC,kBAQO;AARqC;AAQrC,eAPPC,aAOO;AAPqC;AAOrC,eANPC,kBAMO;AANqC;AAMrC,eALPC,eAKO;AALqC;AAKrC,eAJPC,gBAIO;AAJqC;AAIrC,eAFPC,YAEO,GAF6BC,SAE7B;AAEV,eAAKhB,QAAL,GAAgB,KAAKiB,WAAL,CAAiB,CAAjB,EAAoB,IAApB,CAAhB;AACA,eAAKhB,YAAL,GAAoB,KAAKgB,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKf,MAAL,GAAc,KAAKe,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAd;AACA,eAAKd,KAAL,GAAa,KAAKc,WAAL,CAAiB,CAAjB,EAAoB,GAApB,CAAb;AACA,eAAKb,UAAL,GAAkB,KAAKa,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAlB;AACA,eAAKZ,YAAL,GAAoB,KAAKY,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKX,iBAAL,GAAyB,KAAKW,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAzB;AACA,eAAKV,KAAL,GAAa,KAAKU,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAb;AACA,eAAKT,KAAL,GAAa,KAAKS,WAAL,CAAiB,CAAjB,EAAoBvC,KAAK,CAACwC,KAA1B,CAAb;AACA,eAAKT,aAAL,GAAqB,KAAKQ,WAAL,CAAuC,CAAvC,EAA0C;AAAA;AAAA,4DAAqBE,EAA/D,CAArB;AACA,eAAKT,kBAAL,GAA0B,KAAKO,WAAL,CAAiB,EAAjB,EAAqB,IAArB,CAA1B;AACA,eAAKN,aAAL,GAAqB,KAAKM,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAArB;AACA,eAAKL,kBAAL,GAA0B,KAAKK,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAA1B;AACA,eAAKJ,eAAL,GAAuB,KAAKI,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAvB;AACA,eAAKH,gBAAL,GAAwB,KAAKG,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAxB;AACH;;AAEMG,QAAAA,aAAa,CAACC,IAAD,EAAmB;AACnC,eAAKrB,QAAL,CAAcsB,KAAd,GAAsBD,IAAI,CAACrB,QAAL,CAAcuB,IAAd,EAAtB;AACA,eAAKtB,YAAL,CAAkBqB,KAAlB,GAA0BD,IAAI,CAACpB,YAAL,CAAkBsB,IAAlB,EAA1B;AACA,eAAKpB,KAAL,CAAWmB,KAAX,GAAmBD,IAAI,CAAClB,KAAL,CAAWoB,IAAX,EAAnB,CAHmC,CAInC;;AACA,eAAKlB,YAAL,CAAkBiB,KAAlB,GAA0BD,IAAI,CAAChB,YAAL,CAAkBkB,IAAlB,EAA1B;AACA,eAAKjB,iBAAL,CAAuBgB,KAAvB,GAA+BD,IAAI,CAACf,iBAAL,CAAuBiB,IAAvB,EAA/B;AACA,eAAKhB,KAAL,CAAWe,KAAX,GAAmBD,IAAI,CAACd,KAAL,CAAWgB,IAAX,EAAnB,CAPmC,CAQnC;;AACA,eAAKb,kBAAL,CAAwBY,KAAxB,GAAgCD,IAAI,CAACX,kBAArC;AACA,eAAKC,aAAL,CAAmBW,KAAnB,GAA2BD,IAAI,CAACV,aAAhC;AACA,eAAKC,kBAAL,CAAwBU,KAAxB,GAAgCD,IAAI,CAACT,kBAArC;AACA,eAAKC,eAAL,CAAqBS,KAArB,GAA6BD,IAAI,CAACR,eAAlC;AACA,eAAKC,gBAAL,CAAsBQ,KAAtB,GAA8BD,IAAI,CAACP,gBAAnC;AACH;;AAEMU,QAAAA,QAAQ,CAACC,KAAD,EAAwB;AACnC,eAAKC,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAa;AAC9BA,YAAAA,IAAI,CAACN,KAAL,GAAaG,KAAK,CAACI,gBAAN,CAAuBF,CAAvB,CAAb;AACH,WAFD;AAGH;;AAEMG,QAAAA,KAAK,GAAG;AACX;AACA,eAAKJ,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAaA,IAAI,CAACE,KAAL,EAAlC;AACH;;AAjEyD,O,GAoE9D;AACA;;;wBAGaC,M,WAFZnC,OAAO,CAAC,QAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UAEbD,QAAQ,CAAC;AAACmC,QAAAA,IAAI;AAAA;AAAA,8BAAL;AAAgBC,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRpC,QAAQ,CAAC;AAACmC,QAAAA,IAAI,EAAEvD,MAAP;AAAewD,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRpC,QAAQ,CAAC;AAACmC,QAAAA,IAAI;AAAA;AAAA,kCAAL;AAAkBC,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRpC,QAAQ,CAAC;AAACmC,QAAAA,IAAI,EAAErD,MAAP;AAAesD,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,4CAZb,MAEaF,MAFb;AAAA;AAAA,4BAEmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAaxBG,OAbwB,GAaL,KAbK;AAAA,eAcxBC,WAdwB,GAcF,CAdE;AAcS;AAdT,eAexBC,OAfwB;AAAA,eAgBxBC,UAhBwB;AAgBS;AAhBT,eAiBxBtB,YAjBwB,GAiBYC,SAjBZ;AAiByB;AACxD;AAlB+B,eAmBxBY,IAnBwB,GAmBD,IAAIrD,cAAJ,EAnBC;AAAA,eAoBxB+D,WApBwB,GAoBI,EApBJ;AAAA;;AAsB/B;AACqB,YAAVC,UAAU,GAAqB;AAAA;;AACtC,iBAAO,4BAAKxB,YAAL,wCAAmByB,MAAnB,KAA6B;AAAA;AAAA,oDAAiBC,SAArD;AACH;;AACc,YAAJT,IAAI,GAAe;AAAA;;AAC1B,iBAAO,6BAAKjB,YAAL,yCAAmBiB,IAAnB,KAA2B;AAAA;AAAA,wCAAWU,MAA7C;AACH;;AAEDC,QAAAA,MAAM,GAAS;AACX,cAAI,CAAC,KAAKC,KAAV,EAAiB;AACb,iBAAKA,KAAL,GAAa,KAAKC,YAAL;AAAA;AAAA,uCAA8B,KAAKC,YAAL;AAAA;AAAA,mCAA3C;AACH;;AACD,eAAKF,KAAL,CAAWG,EAAX,CAAc;AAAA;AAAA,wCAAWC,iBAAzB,EAA4C,MAAM;AAC9C,gBAAI,KAAKpB,IAAL,CAAUlB,kBAAV,CAA6BY,KAAjC,EAAwC;AACpC;AAAA;AAAA,gDAAa2B,eAAb,CAA6B,IAA7B;AACH;AACJ,WAJD;;AAKA,cAAI,CAAC,KAAKC,QAAV,EAAoB;AAChB,gBAAIC,WAAW,GAAG,KAAKN,YAAL;AAAA;AAAA,iDAAmC,KAAKC,YAAL;AAAA;AAAA,6CAArD;AACA,iBAAKI,QAAL,GAAgBC,WAAhB;AACH;;AAED,eAAKvB,IAAL,CAAUnB,aAAV,CAAwBa,KAAxB,GAAgC,KAAKsB,KAAL,CAAWnC,aAA3C,CAdW,CAeX;;AACA,eAAKmB,IAAL,CAAUf,eAAV,CAA0BkC,EAA1B,CAA8BzB,KAAD,IAAW;AACpC,iBAAKsB,KAAL,CAAW/B,eAAX,GAA6BS,KAA7B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUd,gBAAV,CAA2BiC,EAA3B,CAA+BzB,KAAD,IAAW;AACrC,iBAAKsB,KAAL,CAAW9B,gBAAX,GAA8BQ,KAA9B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUzB,KAAV,CAAgB4C,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,iBAAKsB,KAAL,CAAWzC,KAAX,GAAmBmB,KAAnB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUxB,UAAV,CAAqB2C,EAArB,CAAyBzB,KAAD,IAAW;AAC/B,iBAAKsB,KAAL,CAAWxC,UAAX,GAAwBkB,KAAxB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUvB,YAAV,CAAuB0C,EAAvB,CAA2BzB,KAAD,IAAW;AACjC,iBAAKsB,KAAL,CAAWvC,YAAX,GAA0BiB,KAA1B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUtB,iBAAV,CAA4ByC,EAA5B,CAAgCzB,KAAD,IAAW;AACtC,iBAAKsB,KAAL,CAAWtC,iBAAX,GAA+BgB,KAA/B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUrB,KAAV,CAAgBwC,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,iBAAK8B,IAAL,CAAUC,QAAV,CAAmB/B,KAAnB,EAA0BA,KAA1B,EAAiCA,KAAjC;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUpB,KAAV,CAAgBuC,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,gBAAI,KAAKgC,YAAT,EAAuB;AACnB,mBAAKA,YAAL,CAAkB9C,KAAlB,GAA0Bc,KAA1B;AACH;AACJ,WAJD;AAKH;;AAEMiC,QAAAA,QAAQ,CAACnB,OAAD,EAAmBrB,YAAnB,EAA4D;AACvE,eAAKmB,OAAL,GAAe,IAAf;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,OAAL,GAAeA,OAAf;AACA,eAAKC,UAAL,GAAkBD,OAAO,CAACC,UAA1B;AACA,eAAKtB,YAAL,GAAoBA,YAApB,CALuE,CAOvE;;AACA,cAAMyC,GAAG,GAAGpB,OAAO,CAACqB,SAAR,EAAZ;;AACA,cAAID,GAAJ,EAAS;AAAA;;AACL,gBAAME,gBAAgB,GAAG,6BAAK3C,YAAL,yCAAmByB,MAAnB,KAA6B;AAAA;AAAA,sDAAiBmB,UAAvE;AACA,iBAAKT,QAAL,CAAeU,YAAf,CAA4B,IAA5B;AACA,iBAAKV,QAAL,CAAeW,SAAf,GAA2BH,gBAAgB,GAAG;AAAA;AAAA,wDAAkBI,YAArB,GAAoC;AAAA;AAAA,wDAAkBC,WAAjG;AACA,iBAAKb,QAAL,CAAec,QAAf,GAA0B,IAA1B;AACA,iBAAKC,MAAL,CAAYP,gBAAgB,GAAG;AAAA;AAAA,0CAAWQ,WAAd,GAA4B;AAAA;AAAA,0CAAWC,YAAnE;AACH,WAND,MAMO;AACH;AAAA;AAAA,oCAAQ,SAAR,EAAkB,iBAAlB,EAAsC,eAAtC;AACH;;AAED,eAAKC,eAAL;AACH;;AAEMC,QAAAA,OAAO,GAAG;AACb,eAAKzC,IAAL,CAAU0C,SAAV,CAAoB,IAApB;AACA,eAAK1B,KAAL,CAAW2B,UAAX,CAAsB,IAAtB;AACA,eAAKC,gBAAL;AACH;;AAEOC,QAAAA,WAAW,GAAG;AAClB,cAAI,CAAC,KAAKrB,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUsB,OAA7B,EAAsC;;AAEtC,cAAI9F,MAAJ,EAAY;AACR,iBAAKwE,IAAL,CAAUuB,OAAV;AACH,WAFD,MAEO;AACH;AAAA;AAAA,0CAAWC,UAAX,CAAsB,KAAKxB,IAA3B;AACH;AACJ;;AAEMgB,QAAAA,eAAe,GAAS;AAC3B,cAAI,CAAC,KAAKhC,OAAV,EAAmB;AAEnB,eAAKR,IAAL,CAAUJ,QAAV,CAAmB,KAAKY,OAAL,CAAayC,UAAhC;AACA,eAAKjD,IAAL,CAAU0C,SAAV,CAAoB,IAApB;AACH;;AAEME,QAAAA,gBAAgB,GAAS;AAC5B;AACA,cAAI,KAAKnC,UAAL,IAAmB,KAAKA,UAAL,CAAgByC,cAAhB,CAA+BC,MAA/B,GAAwC,CAA/D,EAAkE;AAC9D,gBAAIC,GAAG,GAAG;AAAA;AAAA,yDAAV;AACAA,YAAAA,GAAG,CAACC,MAAJ,GAAa,IAAb;AACAD,YAAAA,GAAG,CAAC5C,OAAJ,GAAc,KAAKA,OAAnB;;AACA,iBAAK,IAAM8C,SAAX,IAAwB,KAAK7C,UAAL,CAAgByC,cAAxC,EAAwD;AACpD;AAAA;AAAA,gDAAaK,sBAAb,CAAoCH,GAApC,EAAyCE,SAAzC;AACH;AACJ;AACJ;;AAEME,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKnD,OAAV,EAAmB;AAEnB,eAAKC,WAAL,IAAoBkD,EAApB;;AACA,cAAI,KAAKlD,WAAL,GAAmB,KAAKP,IAAL,CAAU5B,QAAV,CAAmBsB,KAA1C,EAAiD;AAC7C;AAAA;AAAA,8CAAa2B,eAAb,CAA6B,IAA7B;AACA;AACH,WAPyB,CAS1B;;;AACA,eAAKL,KAAL,CAAYwC,IAAZ,CAAiBC,EAAE,GAAG,IAAtB;AACA,eAAKzD,IAAL,CAAU0C,SAAV;AACH;;AAEMgB,QAAAA,WAAW,GAAS;AACvB,eAAKpD,OAAL,GAAe,KAAf;;AACA,cAAI,KAAKI,WAAL,IAAoB,KAAKA,WAAL,CAAiByC,MAAjB,GAA0B,CAAlD,EAAqD;AACjD,iBAAKzC,WAAL,CAAiBiD,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACC,OAAN,EAAlC,EADiD,CACG;;AACpD,iBAAKnD,WAAL,GAAmB,EAAnB,CAFiD,CAE1B;AAC1B;;AAED,eAAKM,KAAL,CAAW2B,UAAX,CAAsB,KAAtB;AACA,eAAKrB,QAAL,CAAec,QAAf,GAA0B,KAA1B;AACA,eAAK0B,aAAL;AACA,eAAKC,SAAL;;AACA,cAAI,KAAK/D,IAAL,CAAU3B,YAAV,IAA0B,KAAK2B,IAAL,CAAU3B,YAAV,CAAuBqB,KAAvB,GAA+B,CAA7D,EAAgE;AAC5D,iBAAKsE,YAAL,CAAkB,MAAM;AACpB,mBAAKnB,WAAL;AACH,aAFD,EAEG,KAAK7C,IAAL,CAAU3B,YAAV,CAAuBqB,KAF1B;AAGH,WAJD,MAIO;AACH,iBAAKmD,WAAL;AACH;AACJ;;AAEDoB,QAAAA,SAAS,CAAC3C,QAAD,EAAsB;AAC3B,eAAK4C,MAAL;AACH;;AAEMA,QAAAA,MAAM,GAAG;AACZ;AAAA;AAAA,4CAAa7C,eAAb,CAA6B,IAA7B;AACH;;AAED8C,QAAAA,SAAS,GAAW;AAChB,cAAMvC,GAAG,GAAG,KAAKpB,OAAL,CAAaqB,SAAb,EAAZ;AACA,iBAAOD,GAAG,CAAEuC,SAAL,EAAP;AACH;;AAhL8B,O;;;;;;;;;;iBAKI,I;;;;;;;iBAGD,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, misc, Component, Node, Sprite, Color, Prefab, AudioClip } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Movable, eSpriteDefaultFacing, eMoveEvent } from '../move/Movable';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from './EventGroup';\r\nimport { Property, PropertyContainer } from './PropertyContainer';\r\nimport { Emitter } from './Emitter';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport FCollider, { ColliderGroupType } from 'db://assets/bundles/common/script/game/collider-system/FCollider';\r\nimport FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { eEntityTag } from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { Logger, logWarn } from 'db://assets/scripts/utils/Logger';\r\nimport { ResBullet, BulletSourceType, BulletType } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\n\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nexport class BulletProperty extends PropertyContainer<number> {\r\n    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)\r\n    public delayDestroy!: Property<number>;            // 延迟销毁时间\r\n\r\n    public attack!: Property<number>;                  // 子弹伤害\r\n    public speed!: Property<number>;                   // 子弹速度\r\n    public speedAngle!: Property<number>;              // 子弹速度角度\r\n    public acceleration!: Property<number>;            // 子弹加速度\r\n    public accelerationAngle!: Property<number>;       // 子弹加速度角度\r\n    public scale!: Property<number>;                   // 子弹缩放\r\n    public color!: Property<Color>;                    // 子弹颜色\r\n    public defaultFacing!: Property<eSpriteDefaultFacing>;          // 子弹初始朝向\r\n\r\n    public isDestroyOutScreen!: Property<boolean>;     // 是否超出屏幕销毁\r\n    public isDestructive!: Property<boolean>;          // 是否可被破坏\r\n    public isDestructiveOnHit!: Property<boolean>;     // 命中时是否被销毁\r\n    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向\r\n    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标\r\n\r\n    public bulletConfig: ResBullet|undefined = undefined;\r\n\r\n    constructor() {\r\n        super();\r\n        this.duration = this.addProperty(0, 6000);\r\n        this.delayDestroy = this.addProperty(1, 0);\r\n        this.attack = this.addProperty(2, 1);\r\n        this.speed = this.addProperty(3, 600);\r\n        this.speedAngle = this.addProperty(4, 0);\r\n        this.acceleration = this.addProperty(5, 0);\r\n        this.accelerationAngle = this.addProperty(6, 0);\r\n        this.scale = this.addProperty(7, 1);\r\n        this.color = this.addProperty(8, Color.WHITE);\r\n        this.defaultFacing = this.addProperty<eSpriteDefaultFacing>(9, eSpriteDefaultFacing.Up);\r\n        this.isDestroyOutScreen = this.addProperty(10, true);\r\n        this.isDestructive = this.addProperty(11, false);\r\n        this.isDestructiveOnHit = this.addProperty(12, false);\r\n        this.isFacingMoveDir = this.addProperty(13, false);\r\n        this.isTrackingTarget = this.addProperty(14, false);\r\n    }\r\n\r\n    public resetFromData(data: BulletData) {\r\n        this.duration.value = data.duration.eval(); \r\n        this.delayDestroy.value = data.delayDestroy.eval(); \r\n        this.speed.value = data.speed.eval(); \r\n        // this.speedAngle.value = data.speedAngle.eval(); \r\n        this.acceleration.value = data.acceleration.eval(); \r\n        this.accelerationAngle.value = data.accelerationAngle.eval(); \r\n        this.scale.value = data.scale.eval(); \r\n        // this.color.value = data.color.eval(); \r\n        this.isDestroyOutScreen.value = data.isDestroyOutScreen; \r\n        this.isDestructive.value = data.isDestructive; \r\n        this.isDestructiveOnHit.value = data.isDestructiveOnHit; \r\n        this.isFacingMoveDir.value = data.isFacingMoveDir; \r\n        this.isTrackingTarget.value = data.isTrackingTarget;\r\n    }\r\n\r\n    public copyFrom(other: BulletProperty) {\r\n        this.forEachProperty((k, prop) => {\r\n            prop.value = other.getPropertyValue(k)!;\r\n        });\r\n    }\r\n\r\n    public clear() {\r\n        // Clear all listeners\r\n        this.forEachProperty((k, prop) => prop.clear());\r\n    }\r\n}\r\n\r\n// 子弹 Bullet\r\n// 如何集成到项目里? \r\n@ccclass('Bullet')\r\n@executeInEditMode(true)\r\nexport class Bullet extends Entity {\r\n    @property({type: Movable, displayName: \"移动组件\"})\r\n    public mover!: Movable;\r\n\r\n    @property({type: Sprite, displayName: \"子弹精灵\"})\r\n    public bulletSprite: Sprite|null = null;\r\n\r\n    @property({type: FCollider, displayName: '碰撞组件'})\r\n    public collider: FCollider|null = null;\r\n\r\n    @property({type: Prefab, displayName: \"碰撞特效\"})\r\n    public hitEffectPrefab: Prefab|null = null;\r\n    \r\n    public isAlive: boolean = false;\r\n    public elapsedTime: number = 0;         // 子弹存活时间\r\n    public emitter!: Emitter;\r\n    public bulletData!: BulletData;         // 子弹编辑器下的配置\r\n    public bulletConfig: ResBullet|undefined = undefined;   // 子弹表格配置\r\n    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里\r\n    public prop: BulletProperty = new BulletProperty();\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 增加部分便捷接口获取子弹属性\r\n    public get sourceType(): BulletSourceType {\r\n        return this.bulletConfig?.source || BulletSourceType.MAINPLANE;\r\n    }\r\n    public get type(): BulletType {\r\n        return this.bulletConfig?.type || BulletType.NORMAL;\r\n    }\r\n\r\n    onLoad(): void {\r\n        if (!this.mover) {\r\n            this.mover = this.getComponent(Movable) || this.addComponent(Movable)!;\r\n        }\r\n        this.mover.on(eMoveEvent.onBecomeInvisible, () => {\r\n            if (this.prop.isDestroyOutScreen.value) {\r\n                BulletSystem.onDestroyBullet(this);\r\n            }\r\n        });\r\n        if (!this.collider) {\r\n            let boxCollider = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider)!;\r\n            this.collider = boxCollider;\r\n        }\r\n \r\n        this.prop.defaultFacing.value = this.mover.defaultFacing;\r\n        // listen to property changes\r\n        this.prop.isFacingMoveDir.on((value) => {\r\n            this.mover.isFacingMoveDir = value;\r\n        });\r\n        this.prop.isTrackingTarget.on((value) => {\r\n            this.mover.isTrackingTarget = value;\r\n        });\r\n        this.prop.speed.on((value) => {\r\n            this.mover.speed = value;\r\n        });\r\n        this.prop.speedAngle.on((value) => {\r\n            this.mover.speedAngle = value;\r\n        });\r\n        this.prop.acceleration.on((value) => {\r\n            this.mover.acceleration = value;\r\n        });\r\n        this.prop.accelerationAngle.on((value) => {\r\n            this.mover.accelerationAngle = value;\r\n        });\r\n        this.prop.scale.on((value) => {\r\n            this.node.setScale(value, value, value);\r\n        });\r\n        this.prop.color.on((value) => {\r\n            if (this.bulletSprite) {\r\n                this.bulletSprite.color = value;\r\n            }\r\n        });\r\n    }\r\n\r\n    public onCreate(emitter: Emitter, bulletConfig: ResBullet|undefined): void {\r\n        this.isAlive = true;\r\n        this.elapsedTime = 0;\r\n        this.emitter = emitter;\r\n        this.bulletData = emitter.bulletData!;\r\n        this.bulletConfig = bulletConfig;\r\n\r\n        // TODO: 创建entity的时候，设置正确的tag.\r\n        const ent = emitter.getEntity();\r\n        if (ent) {\r\n            const isShootFromEnemy = this.bulletConfig?.source == BulletSourceType.ENEMYPLANE;\r\n            this.collider!.initBaseData(this);\r\n            this.collider!.groupType = isShootFromEnemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;\r\n            this.collider!.isEnable = true;\r\n            this.addTag(isShootFromEnemy ? eEntityTag.EnemyBullet : eEntityTag.PlayerBullet);\r\n        } else {\r\n            logWarn(\"emitter\",\"onCreate bullet\",  \"has no entity\");\r\n        }\r\n\r\n        this.resetProperties();\r\n    }\r\n\r\n    public onReady() {\r\n        this.prop.notifyAll(true);\r\n        this.mover.setMovable(true);\r\n        this.resetEventGroups();\r\n    }\r\n\r\n    private destroySelf() {\r\n        if (!this.node || !this.node.isValid) return;\r\n        \r\n        if (EDITOR) {\r\n            this.node.destroy();\r\n        } else {\r\n            ObjectPool.returnNode(this.node);\r\n        }\r\n    }\r\n\r\n    public resetProperties(): void {\r\n        if (!this.emitter) return;\r\n\r\n        this.prop.copyFrom(this.emitter.bulletProp);\r\n        this.prop.notifyAll(true);\r\n    }\r\n\r\n    public resetEventGroups(): void {\r\n        // create event groups here\r\n        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {\r\n            let ctx = new EventGroupContext();\r\n            ctx.bullet = this;\r\n            ctx.emitter = this.emitter;\r\n            for (const eventName of this.bulletData.eventGroupData) {\r\n                BulletSystem.createBulletEventGroup(ctx, eventName);\r\n            }\r\n        }\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (!this.isAlive) return;\r\n\r\n        this.elapsedTime += dt;\r\n        if (this.elapsedTime > this.prop.duration.value) {\r\n            BulletSystem.onDestroyBullet(this);\r\n            return;\r\n        }\r\n        \r\n        // 毫秒 -> 秒\r\n        this.mover!.tick(dt / 1000);\r\n        this.prop.notifyAll();\r\n    }\r\n\r\n    public willDestroy(): void {\r\n        this.isAlive = false;\r\n        if (this.eventGroups && this.eventGroups.length > 0) {\r\n            this.eventGroups.forEach(group => group.tryStop()); // stop all event groups before destroying the bullet itself.\r\n            this.eventGroups = []; // clear the event groups array\r\n        }\r\n        \r\n        this.mover.setMovable(false);\r\n        this.collider!.isEnable = false;\r\n        this.removeAllComp();\r\n        this.clearTags();\r\n        if (this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {\r\n            this.scheduleOnce(() => {\r\n                this.destroySelf();\r\n            }, this.prop.delayDestroy.value);\r\n        } else {\r\n            this.destroySelf();\r\n        }\r\n    }\r\n\r\n    onCollide(collider: FCollider) {\r\n        this.remove();\r\n    }\r\n\r\n    public remove() {\r\n        BulletSystem.onDestroyBullet(this);\r\n    }\r\n\r\n    getAttack(): number {\r\n        const ent = this.emitter.getEntity();\r\n        return ent!.getAttack();\r\n    }\r\n}\r\n"]}