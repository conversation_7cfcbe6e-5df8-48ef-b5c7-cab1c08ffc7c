System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, eTargetValueType, eWrapMode, Easing, EventActionBase, _crd;

  function _reportPossibleCrUseOfeTargetValueType(extras) {
    _reporterNs.report("eTargetValueType", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWrapMode(extras) {
    _reporterNs.report("eWrapMode", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventActionData(extras) {
    _reporterNs.report("IEventActionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEasing(extras) {
    _reporterNs.report("Easing", "../Easing", _context.meta, extras);
  }

  _export("EventActionBase", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      eTargetValueType = _unresolved_2.eTargetValueType;
      eWrapMode = _unresolved_2.eWrapMode;
    }, function (_unresolved_3) {
      Easing = _unresolved_3.Easing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "deda2y8lN5AdLCGYccSfjec", "IEventAction", undefined);

      _export("EventActionBase", EventActionBase = class EventActionBase {
        constructor(data) {
          this.data = void 0;
          this._isCompleted = false;
          this._elapsedTime = 0;
          this._startValue = 0;
          this._targetValue = 0;
          // 这里有两个时间：
          // _transitionDuration是从_startValue->_targetValue所需要的时间
          // _duration是整个action执行的生命周期
          this._transitionDuration = 0;
          this._duration = 0;
          this.data = data;
        }

        isCompleted() {
          return this._isCompleted;
        }

        canLerp() {
          return true;
        }

        onLoad(context) {
          this._isCompleted = false;
          this._elapsedTime = 0;
          this._duration = this.data.duration.eval();
          this._transitionDuration = this.data.transitionDuration.eval();
          this.resetStartValue(context);
          this.resetTargetValue(context);
        }

        onExecute(context, dt) {
          this._elapsedTime += dt;

          if (this._elapsedTime >= this._duration) {
            // this.executeInternal(context, this._targetValue);
            this._isCompleted = true;
          } else if (this.canLerp()) {
            this.executeInternal(context, this.lerpValue(this._startValue, this._targetValue));
          }
        }

        lerpValue(startValue, targetValue) {
          if (this._transitionDuration <= 0) {
            return targetValue;
          }

          var progress = this._elapsedTime / this._transitionDuration; // Handle wrap modes when transition duration is exceeded

          if (progress > 1.0) {
            switch (this.data.wrapMode) {
              case (_crd && eWrapMode === void 0 ? (_reportPossibleCrUseOfeWrapMode({
                error: Error()
              }), eWrapMode) : eWrapMode).Once:
                progress = 1.0;
                break;

              case (_crd && eWrapMode === void 0 ? (_reportPossibleCrUseOfeWrapMode({
                error: Error()
              }), eWrapMode) : eWrapMode).Loop:
                progress = progress % 1.0;
                break;

              case (_crd && eWrapMode === void 0 ? (_reportPossibleCrUseOfeWrapMode({
                error: Error()
              }), eWrapMode) : eWrapMode).Pingpong:
                var cycle = Math.floor(progress);
                var localProgress = progress % 1.0;
                progress = cycle % 2 === 0 ? localProgress : 1.0 - localProgress;
                break;
            }
          }

          return (_crd && Easing === void 0 ? (_reportPossibleCrUseOfEasing({
            error: Error()
          }), Easing) : Easing).lerp(this.data.easing, startValue, targetValue, progress);
        } // override this to get the correct start value


        resetStartValue(context) {
          this._startValue = 0;
        }

        resetTargetValue(context) {
          switch (this.data.targetValueType) {
            case (_crd && eTargetValueType === void 0 ? (_reportPossibleCrUseOfeTargetValueType({
              error: Error()
            }), eTargetValueType) : eTargetValueType).Relative:
              this._targetValue = this.data.targetValue.eval() + this._startValue;
              break;

            default:
              this._targetValue = this.data.targetValue.eval();
              break;
          }
        }

        executeInternal(context, value) {// Default implementation does nothing
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4a865ff05e1ef0b803d7b9dbf5ce552f902c61d4.js.map