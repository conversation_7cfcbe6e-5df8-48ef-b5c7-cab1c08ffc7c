System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, Movable, eMoveEvent, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, MovableEventTest;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMovable(extras) {
    _reporterNs.report("Movable", "./Movable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "./Movable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      Movable = _unresolved_2.Movable;
      eMoveEvent = _unresolved_2.eMoveEvent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a10b4SETg1Bs5MpzJWaj+V7", "MovableEventTest", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 测试Movable事件系统的组件
       */

      _export("MovableEventTest", MovableEventTest = (_dec = ccclass('MovableEventTest'), _dec2 = property(Node), _dec(_class = (_class2 = class MovableEventTest extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "movableNode", _descriptor, this);

          this.movable = null;
          this.visibleListener = void 0;
          this.invisibleListener = void 0;
        }

        onLoad() {
          if (this.movableNode) {
            this.movable = this.movableNode.getComponent(_crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
              error: Error()
            }), Movable) : Movable);

            if (this.movable) {
              this.setupEventListeners();
            }
          }
        }

        setupEventListeners() {
          if (!this.movable) return; // 创建监听器函数

          this.visibleListener = () => {
            console.log('MovableEventTest: Object became visible');
          };

          this.invisibleListener = () => {
            console.log('MovableEventTest: Object became invisible');
          }; // 添加事件监听器


          this.movable.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
            error: Error()
          }), eMoveEvent) : eMoveEvent).onBecomeVisible, this.visibleListener);
          this.movable.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
            error: Error()
          }), eMoveEvent) : eMoveEvent).onBecomeInvisible, this.invisibleListener);
        }

        onDestroy() {
          // 清理事件监听器
          if (this.movable) {
            this.movable.off((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
              error: Error()
            }), eMoveEvent) : eMoveEvent).onBecomeVisible, this.visibleListener);
            this.movable.off((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
              error: Error()
            }), eMoveEvent) : eMoveEvent).onBecomeInvisible, this.invisibleListener);
          }
        }
        /**
         * 测试手动触发可见性变化
         */


        testVisibilityChange() {
          if (this.movable) {
            // 切换可见性状态进行测试
            this.movable.setVisible(!this.movable.isVisible);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "movableNode", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7c90f3b616c7f6094f3fbe84ff5412853466cf0b.js.map