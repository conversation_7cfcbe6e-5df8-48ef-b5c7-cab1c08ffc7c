{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/IEventAction.ts"], "names": ["EventActionBase", "eTargetValueType", "eWrapMode", "Easing", "constructor", "data", "_isCompleted", "_elapsedTime", "_startValue", "_targetValue", "_transitionDuration", "_duration", "isCompleted", "canLerp", "onLoad", "context", "duration", "eval", "transitionDuration", "resetStartValue", "resetTargetValue", "onExecute", "dt", "executeInternal", "lerp<PERSON><PERSON>ue", "startValue", "targetValue", "progress", "wrapMode", "Once", "Loop", "<PERSON><PERSON><PERSON>", "cycle", "Math", "floor", "localProgress", "lerp", "easing", "targetValueType", "Relative", "value"], "mappings": ";;;mEAgBaA,e;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAfJC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,S,iBAAAA,S;;AAElBC,MAAAA,M,iBAAAA,M;;;;;;;iCAaIH,e,GAAN,MAAMA,eAAN,CAA8C;AAajDI,QAAAA,WAAW,CAACC,IAAD,EAAyB;AAAA,eAZ3BA,IAY2B;AAAA,eAV1BC,YAU0B,GAVF,KAUE;AAAA,eAT1BC,YAS0B,GATH,CASG;AAAA,eAR1BC,WAQ0B,GARJ,CAQI;AAAA,eAP1BC,YAO0B,GAPH,CAOG;AANpC;AACA;AACA;AAIoC,eAH1BC,mBAG0B,GAHI,CAGJ;AAAA,eAF1BC,SAE0B,GAFN,CAEM;AAChC,eAAKN,IAAL,GAAYA,IAAZ;AACH;;AAEDO,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKN,YAAZ;AACH;;AAEDO,QAAAA,OAAO,GAAY;AACf,iBAAO,IAAP;AACH;;AAEDC,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,eAAKT,YAAL,GAAoB,KAApB;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKI,SAAL,GAAiB,KAAKN,IAAL,CAAUW,QAAV,CAAmBC,IAAnB,EAAjB;AACA,eAAKP,mBAAL,GAA2B,KAAKL,IAAL,CAAUa,kBAAV,CAA6BD,IAA7B,EAA3B;AACA,eAAKE,eAAL,CAAqBJ,OAArB;AACA,eAAKK,gBAAL,CAAsBL,OAAtB;AACH;;AAEDM,QAAAA,SAAS,CAACN,OAAD,EAA6BO,EAA7B,EAA+C;AACpD,eAAKf,YAAL,IAAqBe,EAArB;;AACA,cAAI,KAAKf,YAAL,IAAqB,KAAKI,SAA9B,EAAyC;AACrC;AACA,iBAAKL,YAAL,GAAoB,IAApB;AACH,WAHD,MAIK,IAAI,KAAKO,OAAL,EAAJ,EAAoB;AACrB,iBAAKU,eAAL,CAAqBR,OAArB,EAA8B,KAAKS,SAAL,CAAe,KAAKhB,WAApB,EAAiC,KAAKC,YAAtC,CAA9B;AACH;AACJ;;AAEDe,QAAAA,SAAS,CAACC,UAAD,EAAqBC,WAArB,EAAkD;AACvD,cAAI,KAAKhB,mBAAL,IAA4B,CAAhC,EAAmC;AAC/B,mBAAOgB,WAAP;AACH;;AAED,cAAIC,QAAQ,GAAG,KAAKpB,YAAL,GAAoB,KAAKG,mBAAxC,CALuD,CAMvD;;AACA,cAAIiB,QAAQ,GAAG,GAAf,EAAoB;AAChB,oBAAQ,KAAKtB,IAAL,CAAUuB,QAAlB;AACI,mBAAK;AAAA;AAAA,0CAAUC,IAAf;AACIF,gBAAAA,QAAQ,GAAG,GAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,0CAAUG,IAAf;AACIH,gBAAAA,QAAQ,GAAGA,QAAQ,GAAG,GAAtB;AACA;;AACJ,mBAAK;AAAA;AAAA,0CAAUI,QAAf;AACI,sBAAMC,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWP,QAAX,CAAd;AACA,sBAAMQ,aAAa,GAAGR,QAAQ,GAAG,GAAjC;AACAA,gBAAAA,QAAQ,GAAIK,KAAK,GAAG,CAAR,KAAc,CAAf,GAAoBG,aAApB,GAAqC,MAAMA,aAAtD;AACA;AAXR;AAaH;;AAED,iBAAO;AAAA;AAAA,gCAAOC,IAAP,CAAY,KAAK/B,IAAL,CAAUgC,MAAtB,EAA8BZ,UAA9B,EAA0CC,WAA1C,EAAuDC,QAAvD,CAAP;AACH,SArEgD,CAuEjD;;;AACUR,QAAAA,eAAe,CAACJ,OAAD,EAAmC;AACxD,eAAKP,WAAL,GAAmB,CAAnB;AACH;;AAESY,QAAAA,gBAAgB,CAACL,OAAD,EAAmC;AACzD,kBAAQ,KAAKV,IAAL,CAAUiC,eAAlB;AAEI,iBAAK;AAAA;AAAA,sDAAiBC,QAAtB;AACI,mBAAK9B,YAAL,GAAoB,KAAKJ,IAAL,CAAUqB,WAAV,CAAsBT,IAAtB,KAA+B,KAAKT,WAAxD;AACA;;AACJ;AACI,mBAAKC,YAAL,GAAoB,KAAKJ,IAAL,CAAUqB,WAAV,CAAsBT,IAAtB,EAApB;AACA;AAPR;AASH;;AAESM,QAAAA,eAAe,CAACR,OAAD,EAA6ByB,KAA7B,EAAkD,CACvE;AACH;;AA1FgD,O", "sourcesContent": ["\r\nimport { eTargetValueType, eWrapMode, IEventActionData } from \"../../data/bullet/EventGroupData\";\r\nimport { EventGroupContext } from \"../EventGroup\";\r\nimport { Easing} from \"../Easing\";\r\n\r\nexport interface IEventAction {\r\n    readonly data: IEventActionData;\r\n\r\n    isCompleted(): boolean;\r\n\r\n    onLoad(context: EventGroupContext): void;\r\n    onExecute(context: EventGroupContext, dt: number): void;\r\n\r\n    // onCancel? onComplete?\r\n}\r\n\r\nexport class EventActionBase implements IEventAction {\r\n    readonly data: IEventActionData;\r\n\r\n    protected _isCompleted: boolean = false;\r\n    protected _elapsedTime: number = 0;\r\n    protected _startValue: number = 0;\r\n    protected _targetValue: number = 0;\r\n    // 这里有两个时间：\r\n    // _transitionDuration是从_startValue->_targetValue所需要的时间\r\n    // _duration是整个action执行的生命周期\r\n    protected _transitionDuration: number = 0;\r\n    protected _duration: number = 0;\r\n\r\n    constructor(data: IEventActionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    isCompleted(): boolean {\r\n        return this._isCompleted;\r\n    }\r\n\r\n    canLerp(): boolean {\r\n        return true;\r\n    }\r\n\r\n    onLoad(context: EventGroupContext): void {\r\n        this._isCompleted = false;\r\n        this._elapsedTime = 0;\r\n        this._duration = this.data.duration.eval();\r\n        this._transitionDuration = this.data.transitionDuration.eval();\r\n        this.resetStartValue(context);\r\n        this.resetTargetValue(context);\r\n    }\r\n\r\n    onExecute(context: EventGroupContext, dt: number): void {\r\n        this._elapsedTime += dt;\r\n        if (this._elapsedTime >= this._duration) {\r\n            // this.executeInternal(context, this._targetValue);\r\n            this._isCompleted = true;\r\n        }\r\n        else if (this.canLerp()) {\r\n            this.executeInternal(context, this.lerpValue(this._startValue, this._targetValue));\r\n        }\r\n    }\r\n\r\n    lerpValue(startValue: number, targetValue: number): number {\r\n        if (this._transitionDuration <= 0) {\r\n            return targetValue;\r\n        }\r\n\r\n        let progress = this._elapsedTime / this._transitionDuration;\r\n        // Handle wrap modes when transition duration is exceeded\r\n        if (progress > 1.0) {\r\n            switch (this.data.wrapMode) {\r\n                case eWrapMode.Once:\r\n                    progress = 1.0;\r\n                    break;\r\n                case eWrapMode.Loop:\r\n                    progress = progress % 1.0;\r\n                    break;\r\n                case eWrapMode.Pingpong:\r\n                    const cycle = Math.floor(progress);\r\n                    const localProgress = progress % 1.0;\r\n                    progress = (cycle % 2 === 0) ? localProgress : (1.0 - localProgress);\r\n                    break;\r\n            }\r\n        }\r\n\r\n        return Easing.lerp(this.data.easing, startValue, targetValue, progress);\r\n    }\r\n\r\n    // override this to get the correct start value\r\n    protected resetStartValue(context: EventGroupContext): void {\r\n        this._startValue = 0;\r\n    }\r\n\r\n    protected resetTargetValue(context: EventGroupContext): void {\r\n        switch (this.data.targetValueType)\r\n        {\r\n            case eTargetValueType.Relative:\r\n                this._targetValue = this.data.targetValue.eval() + this._startValue;\r\n                break;\r\n            default:\r\n                this._targetValue = this.data.targetValue.eval();\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // Default implementation does nothing\r\n    }\r\n}\r\n"]}