{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane.ts"], "names": ["_decorator", "EnemyPlaneBase", "GameEnum", "ColliderGroupType", "FBoxCollider", "Tools", "TrackComponent", "ccclass", "property", "EnemyPlane", "_roleIndex", "_curAction", "onLoad", "enemy", "_trackCom", "addScript", "node", "initPlane", "data", "trackData", "EnemyAction", "Track", "_initCollide", "startBattle", "collide<PERSON>omp", "addComponent", "init", "groupType", "ENEMY_NORMAL", "colliderEnabled", "updateHpUI", "setTrackAble", "startTrack", "updateGameLogic", "deltaTime", "isDead", "m_comps", "for<PERSON>ach", "comp", "update", "updateAction", "setAction", "action", "<PERSON><PERSON><PERSON>", "Transform", "AttackPrepare", "playAtkAnim", "AttackIng", "AttackOver", "callback", "plane", "playAnim", "Leave", "to<PERSON><PERSON>", "EnemyDestroyType", "TimeOver"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,c;;AACEC,MAAAA,Q,iBAAAA,Q;;AAGAC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,Y;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,c;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAGTS,U,WADpBF,OAAO,CAAC,YAAD,C,gBAAR,MACqBE,UADrB;AAAA;AAAA,4CACuD;AAAA;AAAA;AAAA,eAEnDC,UAFmD,GAEtC,CAFsC;AAEpC;AAFoC,eAGnDC,UAHmD,GAG9B,CAH8B;AAAA;;AAKzCC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKC,SAAL,GAAiB;AAAA;AAAA,8BAAMC,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB;AACH;;AAEDC,QAAAA,SAAS,CAACC,IAAD,EAAiBC,SAAjB,EAAgC;AACrC,gBAAMF,SAAN,CAAgBC,IAAhB,EAAqBC,SAArB;AACA,eAAKR,UAAL,GAAkB;AAAA;AAAA,oCAASS,WAAT,CAAqBC,KAAvC;;AACA,eAAKC,YAAL;;AACA,eAAKC,WAAL;AACH;;AAEDD,QAAAA,YAAY,GAAS;AACjB;AACA,eAAKE,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKA,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKD,WAAL,CAAkBE,IAAlB,CAAuB,IAAvB;AACA,eAAKF,WAAL,CAAkBG,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,YAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACH;;AAGDN,QAAAA,WAAW,GAAG;AACV,eAAKM,eAAL,GAAuB,IAAvB;AACA,eAAKC,UAAL;;AACA,eAAKhB,SAAL,CAAgBiB,YAAhB,CAA6B,IAA7B;;AACA,eAAKjB,SAAL,CAAgBkB,UAAhB;AACH;;AAEDC,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd;AACA,iBAAKC,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACC,MAAL,CAAYL,SAAZ;AACH,aAFD;;AAGA,iBAAKpB,SAAL,CAAgBmB,eAAhB,CAAgCC,SAAhC;;AACA,iBAAKM,YAAL,CAAkBN,SAAlB;AACH;AACJ,SA1CkD,CA4CnD;AACA;AACA;;;AACAO,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB,cAAI,KAAK/B,UAAL,KAAoB+B,MAAxB,EAAgC;AAC5B,iBAAK/B,UAAL,GAAkB+B,MAAlB,CAD4B,CAG5B;;AACA,iBAAK5B,SAAL,CAAgBiB,YAAhB,CAA6B,IAA7B;;AAEA,oBAAQ,KAAKpB,UAAb;AACI,mBAAK;AAAA;AAAA,wCAASS,WAAT,CAAqBuB,KAA1B;AACI;;AACJ,mBAAK;AAAA;AAAA,wCAASvB,WAAT,CAAqBC,KAA1B;AACI;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAASD,WAAT,CAAqBwB,SAA1B;AACI;AACA;AACA,qBAAKlC,UAAL,GAHJ,CAII;AACA;AACA;AACA;AACA;;AACA,qBAAK+B,SAAL,CAAe;AAAA;AAAA,0CAASrB,WAAT,CAAqBC,KAApC;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAASD,WAAT,CAAqByB,aAA1B;AACI;AACA,qBAAKC,WAAL,CAAiB,MAAM;AACnB,uBAAKL,SAAL,CAAe;AAAA;AAAA,4CAASrB,WAAT,CAAqB2B,SAApC;AACH,iBAFD;AAGA;;AAEJ,mBAAK;AAAA;AAAA,wCAAS3B,WAAT,CAAqB2B,SAA1B;AACI;AACA;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAAS3B,WAAT,CAAqB4B,UAA1B;AACI,qBAAKP,SAAL,CAAe;AAAA;AAAA,0CAASrB,WAAT,CAAqBC,KAApC;AACA;;AACJ;AACI;AAhCR;AAkCH;AACJ;AAED;AACJ;AACA;;;AACIyB,QAAAA,WAAW,CAACG,QAAD,EAAsB;AAC7B,eAAKC,KAAL,CAAYC,QAAZ,CAAsB,MAAK,KAAKzC,UAAW,EAA3C,EAA8C,KAA9C,EAAqD,MAAM;AACvD,iBAAKwC,KAAL,CAAYC,QAAZ,CAAsB,OAAM,KAAKzC,UAAW,EAA5C;AACAuC,YAAAA,QAAQ,QAAR,IAAAA,QAAQ;AACX,WAHD;AAIH;AAED;;;AACAT,QAAAA,YAAY,CAACN,SAAD,EAAoB;AAC5B;AAEA,kBAAQ,KAAKvB,UAAb;AACI,iBAAK;AAAA;AAAA,sCAASS,WAAT,CAAqBuB,KAA1B;AACI,mBAAKd,eAAL,GAAuB,KAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,sCAAST,WAAT,CAAqBC,KAA1B;AACI;AACA;AACA;AACA;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASD,WAAT,CAAqBwB,SAA1B;AACI;;AAEJ,iBAAK;AAAA;AAAA,sCAASxB,WAAT,CAAqByB,aAA1B;AACA,iBAAK;AAAA;AAAA,sCAASzB,WAAT,CAAqB2B,SAA1B;AACI;;AACJ,iBAAK;AAAA;AAAA,sCAAS3B,WAAT,CAAqBgC,KAA1B;AACI,mBAAKC,KAAL,CAAW;AAAA;AAAA,wCAASC,gBAAT,CAA0BC,QAArC;AACA;AAnBR;AAqBH;;AA9HkD,O", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport EnemyPlaneBase from './EnemyPlaneBase';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport { EnemyData } from '../../../data/EnemyData';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport { ColliderGroupType } from '../../../collider-system/FCollider';\r\nimport FBoxCollider from '../../../collider-system/FBoxCollider';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport TrackComponent from '../../base/TrackComponent';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlane')\r\nexport default class EnemyPlane extends EnemyPlaneBase {\r\n\r\n    _roleIndex = 1;//当前形态索引\r\n    _curAction: number = 0;\r\n    \r\n    protected onLoad(): void {\r\n        this.enemy = true\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n    }\r\n\r\n    initPlane(data: EnemyData,trackData:any) {\r\n        super.initPlane(data,trackData);\r\n        this._curAction = GameEnum.EnemyAction.Track;\r\n        this._initCollide();\r\n        this.startBattle();\r\n    }\r\n\r\n    _initCollide(): void {\r\n        // 添加碰撞组件并初始化\r\n        this.collideComp = this.addComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this);\r\n        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n        this.colliderEnabled = false;\r\n    }\r\n\r\n\r\n    startBattle() {\r\n        this.colliderEnabled = true;\r\n        this.updateHpUI();\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number) {\r\n        if (!this.isDead) {\r\n            // 更新所有组件\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(deltaTime);\r\n            });\r\n            this._trackCom!.updateGameLogic(deltaTime);\r\n            this.updateAction(deltaTime);\r\n        }\r\n    }\r\n\r\n    //1.敌机出现，并且移动到指定位置\r\n    //2.开始变形\r\n    //3.开始追踪主角，追踪过程，会射击\r\n    setAction(action: number) {\r\n        if (this._curAction !== action) {\r\n            this._curAction = action;\r\n\r\n            // 停止射击并启用轨迹\r\n            this._trackCom!.setTrackAble(true);\r\n\r\n            switch (this._curAction) {\r\n                case GameEnum.EnemyAction.Sneak:\r\n                    break;\r\n                case GameEnum.EnemyAction.Track:\r\n                    // 跟踪行为\r\n                    break;\r\n                case GameEnum.EnemyAction.Transform:\r\n                    // 变形行为\r\n                    // this._trackCom!.setTrackAble(false);\r\n                    this._roleIndex++;\r\n                    // this.role!.playAnim(\"transform\", () => {\r\n                    //     this.role!.playAnim(\"idle\" + this._roleIndex);\r\n                    //     this.setAction(GameEnum.EnemyAction.Track);\r\n                    //     this._shootCom!.setNextShootAtOnce();\r\n                    // }) || (\r\n                    this.setAction(GameEnum.EnemyAction.Track)\r\n                    break;\r\n                case GameEnum.EnemyAction.AttackPrepare:\r\n                    // 准备攻击行为\r\n                    this.playAtkAnim(() => {\r\n                        this.setAction(GameEnum.EnemyAction.AttackIng);\r\n                    });\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackIng:\r\n                    // 攻击中行为\r\n                    // this._shootCom!.startShoot();\r\n                    break;\r\n                case GameEnum.EnemyAction.AttackOver:\r\n                    this.setAction(GameEnum.EnemyAction.Track);\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n    * 播放攻击动画\r\n    */\r\n    playAtkAnim(callback?: Function) {\r\n        this.plane!.playAnim(`atk${this._roleIndex}`, false, () => {\r\n            this.plane!.playAnim(`idle${this._roleIndex}`);\r\n            callback?.();\r\n        });\r\n    }\r\n\r\n    /**每帧都会检测 */\r\n    updateAction(deltaTime: number) {\r\n        // this._shootCom!.setNextAble(false);\r\n\r\n        switch (this._curAction) {\r\n            case GameEnum.EnemyAction.Sneak:\r\n                this.colliderEnabled = false;\r\n                break;\r\n            case GameEnum.EnemyAction.Track:\r\n                // this._shootCom!.setNextAble(\r\n                //     (this._trackCom!.isMoving && this._enemyData!.bMoveAttack) ||\r\n                //     (!this._trackCom!.isMoving && this._enemyData!.bStayAttack)\r\n                // );\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Transform:\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.AttackPrepare:\r\n            case GameEnum.EnemyAction.AttackIng:\r\n                break;\r\n            case GameEnum.EnemyAction.Leave:\r\n                this.toDie(GameEnum.EnemyDestroyType.TimeOver);\r\n                break;\r\n        }\r\n    }\r\n}"]}