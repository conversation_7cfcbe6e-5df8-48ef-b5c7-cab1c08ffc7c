{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts"], "names": ["_decorator", "GameIns", "GameEnum", "PlaneBase", "Bullet", "Plane", "AttributeConst", "eMoveEvent", "ccclass", "property", "EnemyPlaneBase", "_enemyData", "_trackCom", "_moveCom", "removeAble", "bullets", "moveCom", "initPlane", "data", "trackData", "enemy", "init", "refreshProperty", "initTrack", "trackParams", "offset", "setTrackOverCall", "to<PERSON><PERSON>", "EnemyDestroyType", "TrackOver", "setTrackLeaveCall", "Leave", "initMove", "speed", "angle", "delayDestroy", "speedAngle", "setMovable", "on", "onBecomeInvisible", "scheduleOnce", "_dieWhenOffScreen", "onBecomeVisible", "unschedule", "curHp", "getFinalAttributeByKey", "MaxHP", "maxHp", "getAttack", "Attack", "destroyType", "Die", "colliderEnabled", "onDie", "will<PERSON><PERSON><PERSON>", "playDieAnim", "TimeOver", "callBack", "onCollide", "collider", "isDead", "entity", "attack", "hurtEffectManager", "createHurtNumByType", "node", "getPosition", "hurt", "addBullet", "bullet", "push", "removeBullet", "index", "indexOf", "splice", "setPos", "x", "y", "setPosition"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AAEEC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,c,iBAAAA,c;;AAESC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEZ;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGTU,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ;AAAA;AAAA,yB,2BAFb,MACqBC,cADrB;AAAA;AAAA,kCACsD;AAAA;AAAA;;AAAA;;AAAA,eAGlDC,UAHkD,GAGnB,IAHmB;AAAA,eAIlDC,SAJkD,GAIf,IAJe;AAAA,eAKlDC,QALkD,GAKvB,IALuB;AAAA,eAQlDC,UARkD,GAQ7B,KAR6B;AAAA,eASlDC,OATkD,GAS9B,EAT8B;AAAA;;AAMhC,YAAPC,OAAO,GAAG;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAK9CI,QAAAA,SAAS,CAACC,IAAD,EAAiBC,SAAjB,EAAuC;AAAA,cAAtBA,SAAsB;AAAtBA,YAAAA,SAAsB,GAAN,IAAM;AAAA;;AAC5C,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKT,UAAL,GAAkBO,IAAlB;AACA,gBAAMG,IAAN;AACA,eAAKC,eAAL;;AACA,cAAIH,SAAJ,EAAc;AACV,iBAAKI,SAAL,CAAeJ,SAAf;AACH;AACJ;;AAEDI,QAAAA,SAAS,CAACJ,SAAD,EAAgB;AACrB,eAAKP,SAAL,CAAgBS,IAAhB,CAAqB,IAArB,EAA2BF,SAAS,CAACA,SAArC,EAAgDA,SAAS,CAACK,WAA1D,EAAuEL,SAAS,CAACM,MAAjF;;AAEA,eAAKb,SAAL,CAAgBc,gBAAhB,CAAiC,MAAM;AACnC,iBAAKC,KAAL,CAAW;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,SAArC;AACH,WAFD;;AAIA,eAAKjB,SAAL,CAAgBkB,iBAAhB,CAAkC,MAAM;AACpC,iBAAKH,KAAL,CAAW;AAAA;AAAA,sCAASC,gBAAT,CAA0BG,KAArC;AACH,WAFD;AAGH;;AAEDC,QAAAA,QAAQ,CAACC,KAAD,EAAgBC,KAAhB,EAA+BC,YAA/B,EAAyD;AAAA,cAA1BA,YAA0B;AAA1BA,YAAAA,YAA0B,GAAH,CAAG;AAAA;;AAC7D,eAAKtB,QAAL,CAAeoB,KAAf,GAAuBA,KAAvB;AACA,eAAKpB,QAAL,CAAeuB,UAAf,GAA4BF,KAA5B;;AACA,eAAKrB,QAAL,CAAewB,UAAf,CAA0B,IAA1B;;AAEA,eAAKxB,QAAL,CAAeyB,EAAf,CAAkB;AAAA;AAAA,wCAAWC,iBAA7B,EAAgD,MAAM;AAClD,iBAAKC,YAAL,CAAkB,KAAKC,iBAAvB,EAA0CN,YAA1C;AACH,WAFD;;AAIA,eAAKtB,QAAL,CAAeyB,EAAf,CAAkB;AAAA;AAAA,wCAAWI,eAA7B,EAA8C,MAAM;AAChD,iBAAKC,UAAL,CAAgB,KAAKF,iBAArB;AACH,WAFD;AAGH;;AAEDA,QAAAA,iBAAiB,GAAG;AAChB,eAAKd,KAAL,CAAW;AAAA;AAAA,oCAASC,gBAAT,CAA0BG,KAArC;AACH;;AAEDT,QAAAA,eAAe,GAAG;AAAA;;AACd,eAAKsB,KAAL,uBAAa,KAAKjC,UAAlB,qBAAa,iBAAiBkC,sBAAjB,CAAwC;AAAA;AAAA,gDAAeC,KAAvD,CAAb;AACA,eAAKC,KAAL,GAAa,KAAKH,KAAlB;AACH;;AAEDI,QAAAA,SAAS,GAAU;AAAA;;AACf,iBAAO,2BAAKrC,UAAL,uCAAiBkC,sBAAjB,CAAwC;AAAA;AAAA,gDAAeI,MAAvD,MAAkE,CAAzE;AACH;;AAEDtB,QAAAA,KAAK,CAACuB,WAAD,EAAmF;AAAA,cAAlFA,WAAkF;AAAlFA,YAAAA,WAAkF,GAAzC;AAAA;AAAA,sCAAStB,gBAAT,CAA0BuB,GAAe;AAAA;;AACpF,cAAI,CAAC,MAAMxB,KAAN,CAAYuB,WAAZ,CAAL,EAA+B;AAC3B,mBAAO,KAAP;AACH;;AACD,eAAKE,eAAL,GAAuB,KAAvB;AAEA,eAAKC,KAAL,CAAWH,WAAX;AACA,iBAAO,IAAP;AACH;;AAEDG,QAAAA,KAAK,CAACH,WAAD,EAAsB;AACvB,eAAKI,UAAL;;AAEA,kBAAQJ,WAAR;AACI,iBAAK;AAAA;AAAA,sCAAStB,gBAAT,CAA0BuB,GAA/B;AACI,mBAAKI,WAAL,CAAiB,MAAI;AACjB,qBAAKzC,UAAL,GAAkB,IAAlB;AACH,eAFD;AAGA;;AAEJ,iBAAK;AAAA;AAAA,sCAASc,gBAAT,CAA0BG,KAA/B;AACA,iBAAK;AAAA;AAAA,sCAASH,gBAAT,CAA0BC,SAA/B;AACA,iBAAK;AAAA;AAAA,sCAASD,gBAAT,CAA0B4B,QAA/B;AACI;AAVR;AAYH;;AAEDD,QAAAA,WAAW,CAACE,QAAD,EAAqB;AAC5B;AACA;AACA;AACAA,UAAAA,QAAQ,QAAR,IAAAA,QAAQ;AACX;;AAEDC,QAAAA,SAAS,CAACC,QAAD,EAAsB;AAC3B,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,gBAAID,QAAQ,CAACE,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,kBAAMC,MAAM,GAAGH,QAAQ,CAACE,MAAT,CAAgBb,SAAhB,EAAf;AACA;AAAA;AAAA,sCAAQe,iBAAR,CAA0BC,mBAA1B,CAA8CL,QAAQ,CAACE,MAAT,CAAgBI,IAAhB,CAAqBC,WAArB,EAA9C,EAAkFJ,MAAlF;AACA,mBAAKK,IAAL,CAAUL,MAAV;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIR,QAAAA,UAAU,GAAG,CAEZ;;AAEDc,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB,cAAI,KAAKtD,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAauD,IAAb,CAAkBD,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,YAAY,CAACF,MAAD,EAAiB;AACzB,cAAI,KAAKtD,OAAT,EAAkB;AACd,gBAAMyD,KAAK,GAAG,KAAKzD,OAAL,CAAa0D,OAAb,CAAqBJ,MAArB,CAAd;;AACA,gBAAIG,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAKzD,OAAL,CAAa2D,MAAb,CAAoBF,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;;AAEDG,QAAAA,MAAM,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AACzB,eAAKZ,IAAL,CAAUa,WAAV,CAAsBF,CAAtB,EAAyBC,CAAzB;AACH;;AApIiD,O;;;;;iBAE5B,I", "sourcesContent": ["import { _decorator} from 'cc';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport PlaneBase from '../PlaneBase';\r\nimport FCollider from '../../../collider-system/FCollider';\r\nimport { Bullet } from '../../../bullet/Bullet';\r\nimport { Plane } from 'db://assets/bundles/common/script/ui/Plane';\r\nimport { EnemyData } from '../../../data/EnemyData';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport { Movable, eMoveEvent } from '../../../move/Movable';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlaneBase')\r\nexport default class EnemyPlaneBase extends PlaneBase {\r\n    @property(Plane)//敌机显示组件\r\n    plane: Plane | null = null;\r\n    _enemyData: EnemyData | null = null;\r\n    _trackCom: TrackComponent | null = null;\r\n    _moveCom: Movable | null = null;\r\n    public get moveCom() { return this._moveCom; }\r\n\r\n    removeAble:boolean = false;\r\n    bullets: Bullet[] = [];\r\n\r\n    initPlane(data: EnemyData,trackData:any = null) {\r\n        this.enemy = true\r\n        this._enemyData = data;\r\n        super.init();\r\n        this.refreshProperty();\r\n        if (trackData){\r\n            this.initTrack(trackData);\r\n        }\r\n    }\r\n\r\n    initTrack(trackData:any) {\r\n        this._trackCom!.init(this, trackData.trackData, trackData.trackParams, trackData.offset);\r\n\r\n        this._trackCom!.setTrackOverCall(() => {\r\n            this.toDie(GameEnum.EnemyDestroyType.TrackOver);\r\n        });\r\n\r\n        this._trackCom!.setTrackLeaveCall(() => {\r\n            this.toDie(GameEnum.EnemyDestroyType.Leave);\r\n        });\r\n    }\r\n\r\n    initMove(speed: number, angle: number, delayDestroy: number = 0) {\r\n        this._moveCom!.speed = speed;\r\n        this._moveCom!.speedAngle = angle;\r\n        this._moveCom!.setMovable(true);\r\n\r\n        this._moveCom!.on(eMoveEvent.onBecomeInvisible, () => {\r\n            this.scheduleOnce(this._dieWhenOffScreen, delayDestroy);\r\n        });\r\n\r\n        this._moveCom!.on(eMoveEvent.onBecomeVisible, () => {\r\n            this.unschedule(this._dieWhenOffScreen);\r\n        });\r\n    }\r\n\r\n    _dieWhenOffScreen() {\r\n        this.toDie(GameEnum.EnemyDestroyType.Leave);\r\n    }\r\n\r\n    refreshProperty() {\r\n        this.curHp = this._enemyData?.getFinalAttributeByKey(AttributeConst.MaxHP)!;\r\n        this.maxHp = this.curHp;\r\n    }\r\n\r\n    getAttack():number {\r\n        return this._enemyData?.getFinalAttributeByKey(AttributeConst.Attack) || 0;\r\n    }\r\n\r\n    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean  {\r\n        if (!super.toDie(destroyType)) {\r\n            return false;\r\n        }\r\n        this.colliderEnabled = false;\r\n\r\n        this.onDie(destroyType);\r\n        return true;\r\n    }\r\n\r\n    onDie(destroyType: number) {\r\n        this.willRemove();\r\n\r\n        switch (destroyType) {\r\n            case GameEnum.EnemyDestroyType.Die:\r\n                this.playDieAnim(()=>{\r\n                    this.removeAble = true;\r\n                });\r\n                break;\r\n\r\n            case GameEnum.EnemyDestroyType.Leave:\r\n            case GameEnum.EnemyDestroyType.TrackOver:\r\n            case GameEnum.EnemyDestroyType.TimeOver:\r\n                break;\r\n        }\r\n    }\r\n\r\n    playDieAnim(callBack: Function) {\r\n        // if (this.plane) {\r\n        //     this.plane.playDieAnim(callBack);   \r\n        // }\r\n        callBack?.();\r\n    }\r\n\r\n    onCollide(collider: FCollider) {\r\n        if (!this.isDead) {\r\n            if (collider.entity instanceof Bullet) {\r\n                const attack = collider.entity.getAttack();\r\n                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), attack);\r\n                this.hurt(attack)\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌机\r\n     */\r\n    willRemove() {\r\n\r\n    }\r\n\r\n    addBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 从敌人移除子弹\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    removeBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    setPos(x: number, y: number) {\r\n        this.node.setPosition(x, y);\r\n    }\r\n}"]}