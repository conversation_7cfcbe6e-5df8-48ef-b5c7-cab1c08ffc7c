System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Enum, misc, UITransform, Vec2, Vec3, BulletSystem, _dec, _dec2, _class, _class2, _descriptor, _crd, degreesToRadians, radiansToDegrees, ccclass, property, executeInEditMode, eSpriteDefaultFacing, eMoveEvent, Movable;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMovable(extras) {
    _reporterNs.report("IMovable", "./IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Enum = _cc.Enum;
      misc = _cc.misc;
      UITransform = _cc.UITransform;
      Vec2 = _cc.Vec2;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      BulletSystem = _unresolved_2.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "69d1c91vGlPmascxGbqaXaG", "Movable", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Enum', 'misc', 'Node', 'UITransform', 'Vec2', 'Vec3']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("eSpriteDefaultFacing", eSpriteDefaultFacing = /*#__PURE__*/function (eSpriteDefaultFacing) {
        eSpriteDefaultFacing[eSpriteDefaultFacing["Right"] = 0] = "Right";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Up"] = -90] = "Up";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Down"] = 90] = "Down";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Left"] = 180] = "Left";
        return eSpriteDefaultFacing;
      }({}));

      _export("eMoveEvent", eMoveEvent = /*#__PURE__*/function (eMoveEvent) {
        eMoveEvent[eMoveEvent["onBecomeVisible"] = 0] = "onBecomeVisible";
        eMoveEvent[eMoveEvent["onBecomeInvisible"] = 1] = "onBecomeInvisible";
        return eMoveEvent;
      }({}));

      _export("Movable", Movable = (_dec = ccclass('Movable'), _dec2 = property({
        type: Enum(eSpriteDefaultFacing),
        displayName: '图片默认朝向'
      }), _dec(_class = executeInEditMode(_class = (_class2 = class Movable extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "defaultFacing", _descriptor, this);

          this.isFacingMoveDir = false;
          // 是否朝向行进方向
          this.isTrackingTarget = false;
          // 是否正在追踪目标
          this.speed = 1;
          // 速度
          this.speedAngle = 0;
          // 速度方向 (用角度表示)
          this.turnSpeed = 60;
          // 转向速度（仅用在追踪目标时）
          this.acceleration = 0;
          // 加速度
          this.accelerationAngle = 0;
          // 加速度方向 (用角度表示)
          // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})
          this.tiltSpeed = 0;
          // 偏移速度
          // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})
          this.tiltOffset = 100;
          // 偏移距离
          this.target = null;
          // 追踪的目标节点
          this.arrivalDistance = 10;
          // 到达目标的距离
          this._selfSize = new Vec2();
          this._position = new Vec3();
          this._tiltTime = 0;
          // 用于计算倾斜偏移的累积时间
          this._basePosition = new Vec3();
          // 基础位置（不包含倾斜偏移）
          this._isVisible = true;
          this._isMovable = true;
          // Event system:
          this._eventListeners = new Map();
        }

        // 是否可见
        get isVisible() {
          return this._isVisible;
        }

        // 是否可移动
        get isMovable() {
          return this._isMovable;
        }

        onLoad() {
          const uiTransform = this.node.getComponent(UITransform);
          const self_size = uiTransform ? uiTransform.contentSize : {
            width: 0,
            height: 0
          };

          this._selfSize.set(self_size.width / 2, self_size.height / 2);
        }

        onDestroy() {
          // clear all event listeners
          this._eventListeners.clear();
        }
        /**
         * 添加事件监听器
         * @param event 事件类型
         * @param listener 监听器函数
         */


        on(event, listener) {
          if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
          }

          const listeners = this._eventListeners.get(event);

          if (!listeners.includes(listener)) {
            listeners.push(listener);
          }
        }
        /**
         * 移除事件监听器
         * @param event 事件类型
         * @param listener 监听器函数
         */


        off(event, listener) {
          const listeners = this._eventListeners.get(event);

          if (listeners) {
            const index = listeners.indexOf(listener);

            if (index !== -1) {
              listeners.splice(index, 1);
            }
          }
        }
        /**
         * 触发事件
         * @param event 事件类型
         */


        emit(event) {
          const listeners = this._eventListeners.get(event);

          if (listeners && listeners.length > 0) {
            listeners.forEach(listener => listener());
          }
        }

        tick(dt) {
          if (!this._isMovable) return;
          const angleRadians = degreesToRadians(this.speedAngle); // Convert speed and angle to velocity vector

          let velocityX = this.speed * Math.cos(angleRadians);
          let velocityY = this.speed * Math.sin(angleRadians);

          if (this.isTrackingTarget && this.target) {
            const targetPos = this.target.getPosition();
            const currentPos = this._basePosition; // Calculate direction to target

            const directionX = targetPos.x - currentPos.x;
            const directionY = targetPos.y - currentPos.y;
            const distance = Math.sqrt(directionX * directionX + directionY * directionY);

            if (distance > 0) {
              // Calculate desired angle to target
              const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX)); // Smoothly adjust speedAngle toward target

              const angleDiff = desiredAngle - this.speedAngle; // Normalize angle difference to [-180, 180] range

              const normalizedAngleDiff = (angleDiff + 180) % 360 - 180; // Apply tracking adjustment (you can add a trackingStrength property to control this)

              const trackingStrength = 1.0; // Can be made configurable

              const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable

              const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);
              this.speedAngle += turnAmount * trackingStrength;
              const angleRadiansAfterTracking = degreesToRadians(this.speedAngle); // Recalculate velocity with new angle

              velocityX = this.speed * Math.cos(angleRadiansAfterTracking);
              velocityY = this.speed * Math.sin(angleRadiansAfterTracking);
            }
          } // Convert acceleration and angle to acceleration vector


          if (this.acceleration !== 0) {
            const accelerationRadians = degreesToRadians(this.accelerationAngle);
            const accelerationX = this.acceleration * Math.cos(accelerationRadians);
            const accelerationY = this.acceleration * Math.sin(accelerationRadians); // Update velocity vector: v = v + a * dt

            velocityX += accelerationX * dt;
            velocityY += accelerationY * dt;
          } // Convert back to speed and angle


          this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
          this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX)); // Update position: p = p + v * dt

          if (velocityX !== 0 || velocityY !== 0) {
            // Update base position (main movement path)
            this._basePosition.x += velocityX * dt;
            this._basePosition.y += velocityY * dt; // Start with base position

            this._position.set(this._basePosition); // Apply tilting behavior if enabled


            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
              // Update tilt time
              this._tiltTime += dt; // Calculate perpendicular direction to movement
              // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))

              const moveAngleRad = degreesToRadians(this.speedAngle);
              const perpX = -Math.sin(moveAngleRad);
              const perpY = Math.cos(moveAngleRad); // Calculate tilt offset using sine wave

              const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset; // Apply tilt offset in perpendicular direction (as position offset, not velocity)

              this._position.x += perpX * tiltAmount;
              this._position.y += perpY * tiltAmount;
            }

            this.node.setPosition(this._position);
            this.checkVisibility();
          }

          if (this.isFacingMoveDir && this.speed > 0) {
            const finalAngle = this.speedAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
          }
        }

        checkVisibility() {
          // 这里目前的检查逻辑没有考虑旋转和缩放
          // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的
          const visibleSize = (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).worldBounds;
          this.node.getWorldPosition(this._position);
          const isVisible = this._position.x + this._selfSize.x >= visibleSize.xMin && this._position.x - this._selfSize.x <= visibleSize.xMax && this._position.y - this._selfSize.y <= visibleSize.yMax && this._position.y + this._selfSize.y >= visibleSize.yMin; // debug visibility
          // if (!isVisible) {
          //     console.log("Movable", "checkVisibility", this.node.name + " is not visible");
          //     console.log("Movable", "checkLeftBound  :", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), "<=", visibleSize.xMax);
          //     console.log("Movable", "checkRightBound :", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), ">=", visibleSize.xMin);
          //     console.log("Movable", "checkTopBound   :", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), "<=", visibleSize.yMax);
          //     console.log("Movable", "checkBottomBound:", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), ">=", visibleSize.yMin);
          // }

          this.setVisible(isVisible);
        }

        setVisible(visible) {
          if (this._isVisible === visible) return;
          this._isVisible = visible;

          if (visible) {
            this.emit(eMoveEvent.onBecomeVisible);
          } else {
            this.emit(eMoveEvent.onBecomeInvisible);
          }
        }
        /**
         * Set the target to track
         */


        setTarget(target) {
          this.target = target;
          this.isTrackingTarget = target !== null;
        }

        setMovable(movable) {
          this._isMovable = movable;

          if (this._isMovable) {
            // Initialize base position to current node position
            this.node.getPosition(this._basePosition);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "defaultFacing", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eSpriteDefaultFacing.Up;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9f1d92b0d5938be822dd1685b92e5b0ae2542b94.js.map