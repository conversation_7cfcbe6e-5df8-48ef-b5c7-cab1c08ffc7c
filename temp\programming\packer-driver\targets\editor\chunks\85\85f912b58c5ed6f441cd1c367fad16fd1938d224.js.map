{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/PropertyContainer.ts"], "names": ["Property", "PropertyContainer", "PropertyContainerComponent", "_decorator", "Component", "ccclass", "property", "constructor", "value", "onModified", "_value", "_isDirty", "_listeners", "_onModified", "isDirty", "set<PERSON>irty", "newValue", "on", "listener", "push", "off", "filter", "l", "notify", "force", "for<PERSON>ach", "clear", "length", "_anyPropertyDirty", "_properties", "Map", "anyPropertyDirty", "addProperty", "key", "get", "val", "set", "removeProperty", "delete", "getProperty", "getPropertyValue", "setProperty", "forEachProperty", "callback", "notifyAll", "_propertyContainer"], "mappings": ";;;yFAcaA,Q,EAuDAC,iB,EA0DAC,0B;;;;;;;;;;;;;AA/HJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U,GAE9B;;0BAWaH,Q,GAAN,MAAMA,QAAN,CAAuC;AAOnCO,QAAAA,WAAW,CAACC,KAAD,EAAWC,UAAqC,GAAC,IAAjD,EAAuD;AAAA,eANjEC,MAMiE;AAAA,eALjEC,QAKiE,GAL7C,KAK6C;AAJzE;AAIyE,eAHjEC,UAGiE,GAHzB,EAGyB;AAAA,eAFjEC,WAEiE,GAFtB,IAEsB;AACrE,eAAKH,MAAL,GAAcF,KAAd;AACA,eAAKG,QAAL,GAAgB,KAAhB;AACA,eAAKE,WAAL,GAAmBJ,UAAnB;AACH;;AAEU,YAAPK,OAAO,GAAY;AACnB,iBAAO,KAAKH,QAAZ;AACH;;AAEDI,QAAAA,QAAQ,CAACP,KAAD,EAAuB;AAC3B,eAAKG,QAAL,GAAgBH,KAAhB;AACH;;AAEQ,YAALA,KAAK,GAAM;AACX,iBAAO,KAAKE,MAAZ;AACH;;AAEQ,YAALF,KAAK,CAACQ,QAAD,EAAc;AACnB,cAAI,KAAKN,MAAL,KAAgBM,QAApB,EAA8B;AAE9B,eAAKN,MAAL,GAAcM,QAAd;;AACA,cAAI,KAAKH,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBG,QAAjB;AACH;;AACD,eAAKD,QAAL,CAAc,IAAd;AACH;;AAEME,QAAAA,EAAE,CAACC,QAAD,EAAqC;AAC1C,eAAKN,UAAL,CAAgBO,IAAhB,CAAqBD,QAArB;AACH;;AAEME,QAAAA,GAAG,CAACF,QAAD,EAAqC;AAC3C,eAAKN,UAAL,GAAkB,KAAKA,UAAL,CAAgBS,MAAhB,CAAuBC,CAAC,IAAIA,CAAC,KAAKJ,QAAlC,CAAlB;AACH;;AAEMK,QAAAA,MAAM,CAACC,KAAc,GAAG,KAAlB,EAA+B;AACxC,cAAIA,KAAK,IAAI,KAAKV,OAAlB,EAA2B;AACvB,iBAAKF,UAAL,CAAgBa,OAAhB,CAAwBP,QAAQ,IAAIA,QAAQ,CAAC,KAAKR,MAAN,CAA5C;;AACA,iBAAKK,QAAL,CAAc,KAAd;AACH;AACJ;;AAEMW,QAAAA,KAAK,GAAG;AACX,eAAKd,UAAL,CAAgBe,MAAhB,GAAyB,CAAzB;AACH;;AApDyC,O;;mCAuDjC1B,iB,GAAN,MAAMA,iBAAN,CAA2B;AAAA;AAAA,eACtB2B,iBADsB,GACO,KADP;AAAA,eAEtBC,WAFsB,GAEW,IAAIC,GAAJ,EAFX;AAAA;;AAIH,YAAhBC,gBAAgB,GAAY;AACnC,iBAAO,KAAKH,iBAAZ;AACH;;AAEMI,QAAAA,WAAW,CAAIC,GAAJ,EAAYzB,KAAZ,EAAmC;AACjD,cAAIF,QAAQ,GAAG,KAAKuB,WAAL,CAAiBK,GAAjB,CAAqBD,GAArB,CAAf;;AACA,cAAI3B,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,KAAT,GAAiBA,KAAjB;AACH,WAFD,MAEO;AACHF,YAAAA,QAAQ,GAAG,IAAIN,QAAJ,CAAgBQ,KAAhB,EAAwB2B,GAAD,IAAS;AACvC,mBAAKP,iBAAL,GAAyB,IAAzB;AACH,aAFU,CAAX;;AAGA,iBAAKC,WAAL,CAAiBO,GAAjB,CAAqBH,GAArB,EAA0B3B,QAA1B;AACH;;AAED,iBAAOA,QAAP;AACH;;AAEM+B,QAAAA,cAAc,CAAIJ,GAAJ,EAAkB;AACnC,eAAKJ,WAAL,CAAiBS,MAAjB,CAAwBL,GAAxB;AACH;;AAEMM,QAAAA,WAAW,CAAIN,GAAJ,EAAqC;AACnD,iBAAO,KAAKJ,WAAL,CAAiBK,GAAjB,CAAqBD,GAArB,CAAP;AACH;;AAEMO,QAAAA,gBAAgB,CAAIP,GAAJ,EAA2B;AAC9C;AACA,gBAAM3B,QAAQ,GAAG,KAAKuB,WAAL,CAAiBK,GAAjB,CAAqBD,GAArB,CAAjB;;AACA,iBAAO3B,QAAP,oBAAOA,QAAQ,CAAEE,KAAjB;AACH;;AAEMiC,QAAAA,WAAW,CAAIR,GAAJ,EAAYzB,KAAZ,EAA4B;AAC1C,gBAAMF,QAAQ,GAAG,KAAKuB,WAAL,CAAiBK,GAAjB,CAAqBD,GAArB,CAAjB;;AACA,cAAI3B,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACE,KAAT,GAAiBA,KAAjB;AACH;AACJ;;AAEMkC,QAAAA,eAAe,CAAIC,QAAJ,EAA6D;AAC/E,eAAKd,WAAL,CAAiBJ,OAAjB,CAAyB,CAACnB,QAAD,EAAW2B,GAAX,KAAmBU,QAAQ,CAACV,GAAD,EAAM3B,QAAN,CAApD;AACH;;AAEMsC,QAAAA,SAAS,CAACpB,KAAc,GAAG,KAAlB,EAA+B;AAC3C,eAAKK,WAAL,CAAiBJ,OAAjB,CAAyBnB,QAAQ,IAAIA,QAAQ,CAACiB,MAAT,CAAgBC,KAAhB,CAArC;;AACA,eAAKI,iBAAL,GAAyB,KAAzB;AACH;;AAEMF,QAAAA,KAAK,GAAG;AACX,eAAKG,WAAL,CAAiBH,KAAjB;AACH;;AAtD6B,O,GAyDlC;;;4CACaxB,0B,GAAN,MAAMA,0BAAN,SAA4CE,SAA5C,CAAsD;AAAA;AAAA;AAAA,eACjDyC,kBADiD,GACN,IAAI5C,iBAAJ,EADM;AAAA;;AAG9B,YAAhB8B,gBAAgB,GAAY;AACnC,iBAAO,KAAKc,kBAAL,CAAwBd,gBAA/B;AACH;;AAEMC,QAAAA,WAAW,CAAIC,GAAJ,EAAYzB,KAAZ,EAAmC;AACjD,iBAAO,KAAKqC,kBAAL,CAAwBb,WAAxB,CAAoCC,GAApC,EAAyCzB,KAAzC,CAAP;AACH;;AAEM6B,QAAAA,cAAc,CAAIJ,GAAJ,EAAkB;AACnC,eAAKY,kBAAL,CAAwBR,cAAxB,CAAuCJ,GAAvC;AACH;;AAEMM,QAAAA,WAAW,CAAIN,GAAJ,EAAqC;AACnD,iBAAO,KAAKY,kBAAL,CAAwBN,WAAxB,CAAoCN,GAApC,CAAP;AACH;;AAEMO,QAAAA,gBAAgB,CAAIP,GAAJ,EAA2B;AAC9C,iBAAO,KAAKY,kBAAL,CAAwBL,gBAAxB,CAAyCP,GAAzC,CAAP;AACH;;AAEMQ,QAAAA,WAAW,CAAIR,GAAJ,EAAYzB,KAAZ,EAA4B;AAC1C,eAAKqC,kBAAL,CAAwBJ,WAAxB,CAAoCR,GAApC,EAAyCzB,KAAzC;AACH;;AAEMoC,QAAAA,SAAS,CAACpB,KAAc,GAAG,KAAlB,EAA+B;AAC3C,eAAKqB,kBAAL,CAAwBD,SAAxB,CAAkCpB,KAAlC;AACH;;AAEME,QAAAA,KAAK,GAAG;AACX,eAAKmB,kBAAL,CAAwBnB,KAAxB;AACH;;AAjCwD,O", "sourcesContent": ["import { _decorator, Component } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n// Expression wrapper\r\ninterface Expression {\r\n    raw: string;             // the original expression text\r\n    compiled?: Function;     // compiled JS function\r\n}\r\n\r\nexport interface IProperty {\r\n    get isDirty(): boolean;\r\n    notify(force?: boolean): void;\r\n}\r\n\r\nexport class Property<T> implements IProperty {\r\n    private _value: T;\r\n    private _isDirty: boolean = false;\r\n    // listeners\r\n    private _listeners: Array<(value: T) => void> = [];\r\n    private _onModified: ((value: T) => void) | null = null;\r\n\r\n    public constructor(value: T, onModified: ((value: T) => void)|null=null) {\r\n        this._value = value;\r\n        this._isDirty = false;\r\n        this._onModified = onModified;\r\n    }\r\n\r\n    get isDirty(): boolean {\r\n        return this._isDirty;\r\n    }\r\n\r\n    setDirty(value: boolean): void {\r\n        this._isDirty = value;\r\n    }\r\n\r\n    get value(): T {\r\n        return this._value;\r\n    }\r\n\r\n    set value(newValue: T) {\r\n        if (this._value === newValue) return;\r\n\r\n        this._value = newValue;\r\n        if (this._onModified) {\r\n            this._onModified(newValue);\r\n        }\r\n        this.setDirty(true);\r\n    }\r\n\r\n    public on(listener: (value: T) => void): void {\r\n        this._listeners.push(listener);\r\n    }\r\n\r\n    public off(listener: (value: T) => void): void {\r\n        this._listeners = this._listeners.filter(l => l !== listener);\r\n    }\r\n\r\n    public notify(force: boolean = false): void {\r\n        if (force || this.isDirty) {\r\n            this._listeners.forEach(listener => listener(this._value));\r\n            this.setDirty(false);\r\n        }\r\n    }\r\n\r\n    public clear() {\r\n        this._listeners.length = 0;\r\n    }\r\n}\r\n\r\nexport class PropertyContainer<K> {\r\n    private _anyPropertyDirty: boolean = false;\r\n    private _properties: Map<K, IProperty> = new Map();\r\n\r\n    public get anyPropertyDirty(): boolean {\r\n        return this._anyPropertyDirty;\r\n    }\r\n\r\n    public addProperty<T>(key: K, value: T): Property<T> {\r\n        let property = this._properties.get(key) as Property<T> | undefined;\r\n        if (property) {\r\n            property.value = value;\r\n        } else {\r\n            property = new Property<T>(value, (val) => {\r\n                this._anyPropertyDirty = true;\r\n            });\r\n            this._properties.set(key, property);\r\n        }\r\n\r\n        return property;\r\n    }\r\n\r\n    public removeProperty<T>(key: K): void {\r\n        this._properties.delete(key);\r\n    }\r\n\r\n    public getProperty<T>(key: K): Property<T> | undefined {\r\n        return this._properties.get(key) as Property<T> | undefined;\r\n    }\r\n\r\n    public getPropertyValue<T>(key: K): T | undefined {\r\n        // get property as PropertyValue<T>\r\n        const property = this._properties.get(key) as Property<T> | undefined;\r\n        return property?.value; \r\n    }\r\n\r\n    public setProperty<T>(key: K, value: T): void {\r\n        const property = this._properties.get(key) as Property<T> | undefined;\r\n        if (property) {\r\n            property.value = value;\r\n        }\r\n    }\r\n\r\n    public forEachProperty<T>(callback: (key: K, property: Property<T>) => void): void {\r\n        this._properties.forEach((property, key) => callback(key, property as Property<T>));\r\n    }\r\n\r\n    public notifyAll(force: boolean = false): void {\r\n        this._properties.forEach(property => property.notify(force));\r\n        this._anyPropertyDirty = false;\r\n    }\r\n\r\n    public clear() {\r\n        this._properties.clear();\r\n    }\r\n}\r\n\r\n// Use this one to simplify class hierarchy\r\nexport class PropertyContainerComponent<K> extends Component {\r\n    private _propertyContainer: PropertyContainer<K> = new PropertyContainer<K>();\r\n\r\n    public get anyPropertyDirty(): boolean {\r\n        return this._propertyContainer.anyPropertyDirty;\r\n    }\r\n\r\n    public addProperty<T>(key: K, value: T): Property<T> {\r\n        return this._propertyContainer.addProperty(key, value);\r\n    }\r\n\r\n    public removeProperty<T>(key: K): void {\r\n        this._propertyContainer.removeProperty(key);\r\n    }\r\n\r\n    public getProperty<T>(key: K): Property<T> | undefined {\r\n        return this._propertyContainer.getProperty(key);\r\n    }\r\n\r\n    public getPropertyValue<T>(key: K): T | undefined {\r\n        return this._propertyContainer.getPropertyValue(key);\r\n    }\r\n\r\n    public setProperty<T>(key: K, value: T): void {\r\n        this._propertyContainer.setProperty(key, value);\r\n    }\r\n\r\n    public notifyAll(force: boolean = false): void {\r\n        this._propertyContainer.notifyAll(force);\r\n    }\r\n    \r\n    public clear() {\r\n        this._propertyContainer.clear();\r\n    }\r\n}"]}