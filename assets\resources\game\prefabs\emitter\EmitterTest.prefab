[{"__type__": "cc.Prefab", "_name": "EmitterTest", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "EmitterTest", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47gxN2wmBJWafektlAtBbW"}, {"__type__": "2564dArcRFKZKoo3odCQrHw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "bulletID": 0, "emitterData": {"__id__": 6}, "bulletData": {"__id__": 21}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9dgckUIxKrpPzAiNtZlAQ"}, {"__type__": "EmitterData", "isOnlyInScreen": true, "isPreWarm": false, "isLoop": true, "initialDelay": {"__id__": 7}, "preWarmDuration": {"__id__": 8}, "preWarmEffect": null, "emitDuration": {"__id__": 9}, "emitInterval": {"__id__": 10}, "emitPower": {"__id__": 11}, "loopInterval": {"__id__": 12}, "perEmitCount": {"__id__": 13}, "perEmitInterval": {"__id__": 14}, "perEmitOffsetX": {"__id__": 15}, "angle": {"__id__": 16}, "count": {"__id__": 18}, "arc": {"__id__": 19}, "radius": {"__id__": 20}, "emitEffect": null, "eventGroupData": []}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 10000, "isExpression": false, "expression": "10000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 200, "isExpression": false, "expression": "200", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 270, "isExpression": false, "expression": "270", "serializedProgram": {"__id__": 17}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 12, 2], "consts": [120, 30], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 12, "isExpression": false, "expression": "12", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 360, "isExpression": false, "expression": "360", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "BulletData", "isFacingMoveDir": true, "isTrackingTarget": false, "isDestroyOutScreen": false, "isDestructive": false, "isDestructiveOnHit": false, "scale": {"__id__": 22}, "duration": {"__id__": 23}, "delayDestroy": {"__id__": 24}, "speed": {"__id__": 25}, "acceleration": {"__id__": 27}, "accelerationAngle": {"__id__": 28}, "eventGroupData": [{"__id__": 29}, {"__id__": 40}]}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 10000, "isExpression": false, "expression": "10000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 500, "isExpression": false, "expression": "500", "serializedProgram": {"__id__": 26}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 12, 2], "consts": [300, 1000], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "EventGroupData", "name": "", "triggerCount": 1, "conditions": [{"__id__": 30}], "actions": [{"__id__": 32}, {"__id__": 36}]}, {"__type__": "EventConditionData", "op": 0, "type": 101, "compareOp": 3, "targetValue": {"__id__": 31}}, {"__type__": "ExpressionValue", "value": 3000, "isExpression": false, "expression": "3000", "serializedProgram": null}, {"__type__": "EventActionData", "type": 105, "duration": {"__id__": 33}, "targetValue": {"__id__": 34}, "targetValueType": 2, "transitionDuration": {"__id__": 35}, "wrapMode": 1, "easing": 0}, {"__type__": "ExpressionValue", "value": 10000, "isExpression": false, "expression": "10000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 2000, "isExpression": false, "expression": "2000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 2000, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "EventActionData", "type": 106, "duration": {"__id__": 37}, "targetValue": {"__id__": 38}, "targetValueType": 2, "transitionDuration": {"__id__": 39}, "wrapMode": 1, "easing": 0}, {"__type__": "ExpressionValue", "value": 5000, "isExpression": false, "expression": "10000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 360, "isExpression": false, "expression": "360", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 2000, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "EventGroupData", "name": "baozha", "triggerCount": 1, "conditions": [{"__id__": 41}], "actions": []}, {"__type__": "EventConditionData", "op": 0, "type": 101, "compareOp": 2, "targetValue": {"__id__": 42}}, {"__type__": "ExpressionValue", "value": 5000, "isExpression": false, "expression": "5000", "serializedProgram": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1caDlMXuZFcaUvwOgfs0Gy", "targetOverrides": null}]