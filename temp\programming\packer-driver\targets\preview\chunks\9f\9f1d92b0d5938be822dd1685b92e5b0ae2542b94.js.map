{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/Movable.ts"], "names": ["_decorator", "Component", "Enum", "misc", "UITransform", "Vec2", "Vec3", "BulletSystem", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "eSpriteDefaultFacing", "eMoveEvent", "Movable", "type", "displayName", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "turnSpeed", "acceleration", "accelerationAngle", "tiltSpeed", "tiltOffset", "target", "arrivalDistance", "_selfSize", "_position", "_tiltTime", "_basePosition", "_isVisible", "_isMovable", "_eventListeners", "Map", "isVisible", "isMovable", "onLoad", "uiTransform", "node", "getComponent", "self_size", "contentSize", "width", "height", "set", "onDestroy", "clear", "on", "event", "listener", "has", "listeners", "get", "includes", "push", "off", "index", "indexOf", "splice", "emit", "length", "for<PERSON>ach", "tick", "dt", "speedRadians", "velocityX", "Math", "cos", "velocityY", "sin", "targetPos", "getPosition", "currentPos", "directionX", "x", "directionY", "y", "distance", "sqrt", "desiredAngle", "atan2", "angleDiff", "normalizedAngleDiff", "trackingStrength", "maxTurnRate", "turnAmount", "min", "abs", "sign", "accelerationRadians", "accelerationX", "accelerationY", "moveAngleRad", "perpX", "perpY", "tiltAmount", "setPosition", "checkVisibility", "movementAngle", "finalAngle", "defaultFacing", "setRotationFromEuler", "visibleSize", "worldBounds", "getWorldPosition", "xMin", "xMax", "yMax", "yMin", "setVisible", "visible", "onBecomeVisible", "onBecomeInvisible", "<PERSON><PERSON><PERSON><PERSON>", "setMovable", "movable", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAC5DC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCN,I;OACzC;AAAEO,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;sCAErCa,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;4BAOAC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;yBAOCC,O,WAFZL,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEd,IAAI,CAACW,oBAAD,CAAZ;AAAoCI,QAAAA,WAAW,EAAE;AAAjD,OAAD,C,gBAHZL,iB,qBADD,MAEaG,OAFb,SAE6Bd,SAF7B,CAE2D;AAAA;AAAA;;AAAA;;AAAA,eAKhDiB,eALgD,GAKrB,KALqB;AAKT;AALS,eAMhDC,gBANgD,GAMpB,KANoB;AAMT;AANS,eAOhDC,KAPgD,GAOhC,CAPgC;AAOT;AAPS,eAQhDC,UARgD,GAQ3B,CAR2B;AAQT;AARS,eAShDC,SATgD,GAS5B,EAT4B;AAST;AATS,eAUhDC,YAVgD,GAUzB,CAVyB;AAUT;AAVS,eAWhDC,iBAXgD,GAWpB,CAXoB;AAWT;AAE9C;AAbuD,eAchDC,SAdgD,GAc5B,CAd4B;AAcT;AAC9C;AAfuD,eAgBhDC,UAhBgD,GAgB3B,GAhB2B;AAgBR;AAhBQ,eAkBhDC,MAlBgD,GAkB1B,IAlB0B;AAkBT;AAlBS,eAmBhDC,eAnBgD,GAmBtB,EAnBsB;AAmBT;AAnBS,eAqB/CC,SArB+C,GAqB7B,IAAIxB,IAAJ,EArB6B;AAAA,eAsB/CyB,SAtB+C,GAsB7B,IAAIxB,IAAJ,EAtB6B;AAAA,eAuB/CyB,SAvB+C,GAuB3B,CAvB2B;AAuBT;AAvBS,eAwB/CC,aAxB+C,GAwBzB,IAAI1B,IAAJ,EAxByB;AAwBT;AAxBS,eA0B/C2B,UA1B+C,GA0BzB,IA1ByB;AAAA,eA4B/CC,UA5B+C,GA4BzB,IA5ByB;AA+BvD;AA/BuD,eAgC/CC,eAhC+C,GAgCO,IAAIC,GAAJ,EAhCP;AAAA;;AA0BT;AAC1B,YAATC,SAAS,GAAG;AAAE,iBAAO,KAAKJ,UAAZ;AAAyB;;AACJ;AAC1B,YAATK,SAAS,GAAG;AAAE,iBAAO,KAAKJ,UAAZ;AAAyB;;AAKlDK,QAAAA,MAAM,GAAG;AACL,cAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBtC,WAAvB,CAApB;AACA,cAAMuC,SAAS,GAAGH,WAAW,GAAGA,WAAW,CAACI,WAAf,GAA6B;AAAEC,YAAAA,KAAK,EAAE,CAAT;AAAYC,YAAAA,MAAM,EAAE;AAApB,WAA1D;;AACA,eAAKjB,SAAL,CAAekB,GAAf,CAAmBJ,SAAS,CAACE,KAAV,GAAkB,CAArC,EAAwCF,SAAS,CAACG,MAAV,GAAmB,CAA3D;AACH;;AAEDE,QAAAA,SAAS,GAAG;AACR;AACA,eAAKb,eAAL,CAAqBc,KAArB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,EAAE,CAACC,KAAD,EAAoBC,QAApB,EAAgD;AACrD,cAAI,CAAC,KAAKjB,eAAL,CAAqBkB,GAArB,CAAyBF,KAAzB,CAAL,EAAsC;AAClC,iBAAKhB,eAAL,CAAqBY,GAArB,CAAyBI,KAAzB,EAAgC,EAAhC;AACH;;AACD,cAAMG,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAI,CAACG,SAAS,CAACE,QAAV,CAAmBJ,QAAnB,CAAL,EAAmC;AAC/BE,YAAAA,SAAS,CAACG,IAAV,CAAeL,QAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACWM,QAAAA,GAAG,CAACP,KAAD,EAAoBC,QAApB,EAAgD;AACtD,cAAME,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAJ,EAAe;AACX,gBAAMK,KAAK,GAAGL,SAAS,CAACM,OAAV,CAAkBR,QAAlB,CAAd;;AACA,gBAAIO,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdL,cAAAA,SAAS,CAACO,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACYG,QAAAA,IAAI,CAACX,KAAD,EAA0B;AAClC,cAAMG,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAS,IAAIA,SAAS,CAACS,MAAV,GAAmB,CAApC,EAAuC;AACnCT,YAAAA,SAAS,CAACU,OAAV,CAAkBZ,QAAQ,IAAIA,QAAQ,EAAtC;AACH;AACJ;;AAEMa,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKhC,UAAV,EAAsB;AAEtB,cAAMiC,YAAY,GAAG3D,gBAAgB,CAAC,KAAKa,UAAN,CAArC,CAH0B,CAI1B;;AACA,cAAI+C,SAAS,GAAG,KAAKhD,KAAL,GAAaiD,IAAI,CAACC,GAAL,CAASH,YAAT,CAA7B;AACA,cAAII,SAAS,GAAG,KAAKnD,KAAL,GAAaiD,IAAI,CAACG,GAAL,CAASL,YAAT,CAA7B;;AAEA,cAAI,KAAKhD,gBAAL,IAAyB,KAAKQ,MAAlC,EAA0C;AACtC,gBAAM8C,SAAS,GAAG,KAAK9C,MAAL,CAAY+C,WAAZ,EAAlB;AACA,gBAAMC,UAAU,GAAG,KAAKlC,IAAL,CAAUiC,WAAV,EAAnB,CAFsC,CAItC;;AACA,gBAAME,UAAU,GAAGH,SAAS,CAACI,CAAV,GAAcF,UAAU,CAACE,CAA5C;AACA,gBAAMC,UAAU,GAAGL,SAAS,CAACM,CAAV,GAAcJ,UAAU,CAACI,CAA5C;AACA,gBAAMC,QAAQ,GAAGX,IAAI,CAACY,IAAL,CAAUL,UAAU,GAAGA,UAAb,GAA0BE,UAAU,GAAGA,UAAjD,CAAjB;;AAEA,gBAAIE,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA,kBAAME,YAAY,GAAGzE,gBAAgB,CAAC4D,IAAI,CAACc,KAAL,CAAWL,UAAX,EAAuBF,UAAvB,CAAD,CAArC,CAFc,CAId;;AACA,kBAAMQ,SAAS,GAAGF,YAAY,GAAG,KAAK7D,UAAtC,CALc,CAMd;;AACA,kBAAMgE,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPc,CASd;;AACA,kBAAME,gBAAgB,GAAG,GAAzB,CAVc,CAUgB;;AAC9B,kBAAMC,WAAW,GAAG,KAAKjE,SAAzB,CAXc,CAWsB;;AACpC,kBAAMkE,UAAU,GAAGnB,IAAI,CAACoB,GAAL,CAASpB,IAAI,CAACqB,GAAL,CAASL,mBAAT,CAAT,EAAwCE,WAAW,GAAGrB,EAAtD,IAA4DG,IAAI,CAACsB,IAAL,CAAUN,mBAAV,CAA/E;AAEA,mBAAKhE,UAAL,IAAmBmE,UAAU,GAAGF,gBAAhC,CAdc,CAgBd;;AACAlB,cAAAA,SAAS,GAAG,KAAKhD,KAAL,GAAaiD,IAAI,CAACC,GAAL,CAAS9D,gBAAgB,CAAC,KAAKa,UAAN,CAAzB,CAAzB;AACAkD,cAAAA,SAAS,GAAG,KAAKnD,KAAL,GAAaiD,IAAI,CAACG,GAAL,CAAShE,gBAAgB,CAAC,KAAKa,UAAN,CAAzB,CAAzB;AACH;AACJ,WArCyB,CAuC1B;;;AACA,cAAI,KAAKE,YAAL,KAAsB,CAA1B,EAA6B;AACzB,gBAAMqE,mBAAmB,GAAGpF,gBAAgB,CAAC,KAAKgB,iBAAN,CAA5C;AACA,gBAAMqE,aAAa,GAAG,KAAKtE,YAAL,GAAoB8C,IAAI,CAACC,GAAL,CAASsB,mBAAT,CAA1C;AACA,gBAAME,aAAa,GAAG,KAAKvE,YAAL,GAAoB8C,IAAI,CAACG,GAAL,CAASoB,mBAAT,CAA1C,CAHyB,CAIzB;;AACAxB,YAAAA,SAAS,IAAIyB,aAAa,GAAG3B,EAA7B;AACAK,YAAAA,SAAS,IAAIuB,aAAa,GAAG5B,EAA7B;AACH,WA/CyB,CAiD1B;;;AACA,eAAK9C,KAAL,GAAaiD,IAAI,CAACY,IAAL,CAAUb,SAAS,GAAGA,SAAZ,GAAwBG,SAAS,GAAGA,SAA9C,CAAb;AACA,eAAKlD,UAAL,GAAkBZ,gBAAgB,CAAC4D,IAAI,CAACc,KAAL,CAAWZ,SAAX,EAAsBH,SAAtB,CAAD,CAAlC,CAnD0B,CAqD1B;;AACA,cAAIA,SAAS,KAAK,CAAd,IAAmBG,SAAS,KAAK,CAArC,EAAwC;AACpC;AACA,iBAAKvC,aAAL,CAAmB6C,CAAnB,IAAwBT,SAAS,GAAGF,EAApC;AACA,iBAAKlC,aAAL,CAAmB+C,CAAnB,IAAwBR,SAAS,GAAGL,EAApC,CAHoC,CAKpC;;AACA,iBAAKpC,SAAL,CAAeiB,GAAf,CAAmB,KAAKf,aAAxB,EANoC,CAQpC;;;AACA,gBAAI,KAAKP,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C;AACA,mBAAKK,SAAL,IAAkBmC,EAAlB,CAF2C,CAI3C;AACA;;AACA,kBAAM6B,YAAY,GAAGvF,gBAAgB,CAAC,KAAKa,UAAN,CAArC;AACA,kBAAM2E,KAAK,GAAG,CAAC3B,IAAI,CAACG,GAAL,CAASuB,YAAT,CAAf;AACA,kBAAME,KAAK,GAAG5B,IAAI,CAACC,GAAL,CAASyB,YAAT,CAAd,CAR2C,CAU3C;;AACA,kBAAMG,UAAU,GAAG7B,IAAI,CAACG,GAAL,CAAS,KAAKzC,SAAL,GAAiB,KAAKN,SAA/B,IAA4C,KAAKC,UAApE,CAX2C,CAa3C;;AACA,mBAAKI,SAAL,CAAe+C,CAAf,IAAoBmB,KAAK,GAAGE,UAA5B;AACA,mBAAKpE,SAAL,CAAeiD,CAAf,IAAoBkB,KAAK,GAAGC,UAA5B;AACH;;AAED,iBAAKzD,IAAL,CAAU0D,WAAV,CAAsB,KAAKrE,SAA3B;AACA,iBAAKsE,eAAL;AACH;;AAED,cAAI,KAAKlF,eAAL,IAAwB,KAAKE,KAAL,GAAa,CAAzC,EAA4C;AACxC,gBAAMiF,aAAa,GAAG5F,gBAAgB,CAAC4D,IAAI,CAACc,KAAL,CAAWZ,SAAX,EAAsBH,SAAtB,CAAD,CAAtC;AACA,gBAAMkC,UAAU,GAAGD,aAAa,GAAG,KAAKE,aAAxC;AACA,iBAAK9D,IAAL,CAAU+D,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;AACJ;;AAEMF,QAAAA,eAAe,GAAS;AAC3B;AACA;AACA,cAAMK,WAAW,GAAG;AAAA;AAAA,4CAAaC,WAAjC;AACA,eAAKjE,IAAL,CAAUkE,gBAAV,CAA2B,KAAK7E,SAAhC;AACA,cAAMO,SAAS,GAAI,KAAKP,SAAL,CAAe+C,CAAf,GAAmB,KAAKhD,SAAL,CAAegD,CAAnC,IAAyC4B,WAAW,CAACG,IAArD,IACb,KAAK9E,SAAL,CAAe+C,CAAf,GAAmB,KAAKhD,SAAL,CAAegD,CAAnC,IAAyC4B,WAAW,CAACI,IADvC,IAEb,KAAK/E,SAAL,CAAeiD,CAAf,GAAmB,KAAKlD,SAAL,CAAekD,CAAnC,IAAyC0B,WAAW,CAACK,IAFvC,IAGb,KAAKhF,SAAL,CAAeiD,CAAf,GAAmB,KAAKlD,SAAL,CAAekD,CAAnC,IAAyC0B,WAAW,CAACM,IAHzD,CAL2B,CAU3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAKC,UAAL,CAAgB3E,SAAhB;AACH;;AAEM2E,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAKhF,UAAL,KAAoBgF,OAAxB,EAAiC;AAEjC,eAAKhF,UAAL,GAAkBgF,OAAlB;;AACA,cAAIA,OAAJ,EAAa;AACT,iBAAKnD,IAAL,CAAUhD,UAAU,CAACoG,eAArB;AACH,WAFD,MAEO;AACH,iBAAKpD,IAAL,CAAUhD,UAAU,CAACqG,iBAArB;AACH;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACzF,MAAD,EAA4B;AACxC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKR,gBAAL,GAAwBQ,MAAM,KAAK,IAAnC;AACH;;AAEM0F,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,eAAKpF,UAAL,GAAkBoF,OAAlB;;AAEA,cAAI,KAAKpF,UAAT,EAAqB;AACjB;AACA,iBAAKO,IAAL,CAAUiC,WAAV,CAAsB,KAAK1C,aAA3B;AACH;AACJ;;AAlOsD,O;;;;;iBAGVnB,oBAAoB,CAAC0G,E", "sourcesContent": ["import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3 } from 'cc';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport { IMovable } from './IMovable';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\nexport enum eMoveEvent {\r\n    onBecomeVisible,\r\n    onBecomeInvisible,\r\n}\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    @property({ type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向' })\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向\r\n    public isTrackingTarget: boolean = false;     // 是否正在追踪目标\r\n    public speed: number = 1;                     // 速度\r\n    public speedAngle: number = 0;                // 速度方向 (用角度表示)\r\n    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）\r\n    public acceleration: number = 0;              // 加速度\r\n    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)\r\n\r\n    // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})\r\n    public tiltSpeed: number = 0;                 // 偏移速度\r\n    // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})\r\n    public tiltOffset: number = 100;               // 偏移距离\r\n\r\n    public target: Node | null = null;            // 追踪的目标节点\r\n    public arrivalDistance: number = 10;          // 到达目标的距离\r\n\r\n    private _selfSize: Vec2 = new Vec2();\r\n    private _position: Vec3 = new Vec3();\r\n    private _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间\r\n    private _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）\r\n\r\n    private _isVisible: boolean = true;           // 是否可见\r\n    public get isVisible() { return this._isVisible; }\r\n    private _isMovable: boolean = true;           // 是否可移动\r\n    public get isMovable() { return this._isMovable; }\r\n\r\n    // Event system:\r\n    private _eventListeners: Map<eMoveEvent, Array<() => void>> = new Map();\r\n\r\n    onLoad() {\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        const self_size = uiTransform ? uiTransform.contentSize : { width: 0, height: 0 };\r\n        this._selfSize.set(self_size.width / 2, self_size.height / 2);\r\n    }\r\n\r\n    onDestroy() {\r\n        // clear all event listeners\r\n        this._eventListeners.clear();\r\n    }\r\n\r\n    /**\r\n     * 添加事件监听器\r\n     * @param event 事件类型\r\n     * @param listener 监听器函数\r\n     */\r\n    public on(event: eMoveEvent, listener: () => void): void {\r\n        if (!this._eventListeners.has(event)) {\r\n            this._eventListeners.set(event, []);\r\n        }\r\n        const listeners = this._eventListeners.get(event)!;\r\n        if (!listeners.includes(listener)) {\r\n            listeners.push(listener);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除事件监听器\r\n     * @param event 事件类型\r\n     * @param listener 监听器函数\r\n     */\r\n    public off(event: eMoveEvent, listener: () => void): void {\r\n        const listeners = this._eventListeners.get(event);\r\n        if (listeners) {\r\n            const index = listeners.indexOf(listener);\r\n            if (index !== -1) {\r\n                listeners.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 触发事件\r\n     * @param event 事件类型\r\n     */\r\n    private emit(event: eMoveEvent): void {\r\n        const listeners = this._eventListeners.get(event);\r\n        if (listeners && listeners.length > 0) {\r\n            listeners.forEach(listener => listener());\r\n        }\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (!this._isMovable) return;\r\n\r\n        const speedRadians = degreesToRadians(this.speedAngle);\r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(speedRadians);\r\n        let velocityY = this.speed * Math.sin(speedRadians);\r\n\r\n        if (this.isTrackingTarget && this.target) {\r\n            const targetPos = this.target.getPosition();\r\n            const currentPos = this.node.getPosition();\r\n\r\n            // Calculate direction to target\r\n            const directionX = targetPos.x - currentPos.x;\r\n            const directionY = targetPos.y - currentPos.y;\r\n            const distance = Math.sqrt(directionX * directionX + directionY * directionY);\r\n\r\n            if (distance > 0) {\r\n                // Calculate desired angle to target\r\n                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n\r\n                // Smoothly adjust speedAngle toward target\r\n                const angleDiff = desiredAngle - this.speedAngle;\r\n                // Normalize angle difference to [-180, 180] range\r\n                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n\r\n                // Apply tracking adjustment (you can add a trackingStrength property to control this)\r\n                const trackingStrength = 1.0; // Can be made configurable\r\n                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable\r\n                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n\r\n                this.speedAngle += turnAmount * trackingStrength;\r\n\r\n                // Recalculate velocity with new angle\r\n                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n            }\r\n        }\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        if (this.acceleration !== 0) {\r\n            const accelerationRadians = degreesToRadians(this.accelerationAngle);\r\n            const accelerationX = this.acceleration * Math.cos(accelerationRadians);\r\n            const accelerationY = this.acceleration * Math.sin(accelerationRadians);\r\n            // Update velocity vector: v = v + a * dt\r\n            velocityX += accelerationX * dt;\r\n            velocityY += accelerationY * dt;\r\n        }\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        if (velocityX !== 0 || velocityY !== 0) {\r\n            // Update base position (main movement path)\r\n            this._basePosition.x += velocityX * dt;\r\n            this._basePosition.y += velocityY * dt;\r\n\r\n            // Start with base position\r\n            this._position.set(this._basePosition);\r\n\r\n            // Apply tilting behavior if enabled\r\n            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\r\n                // Update tilt time\r\n                this._tiltTime += dt;\r\n\r\n                // Calculate perpendicular direction to movement\r\n                // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))\r\n                const moveAngleRad = degreesToRadians(this.speedAngle);\r\n                const perpX = -Math.sin(moveAngleRad);\r\n                const perpY = Math.cos(moveAngleRad);\r\n\r\n                // Calculate tilt offset using sine wave\r\n                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\r\n\r\n                // Apply tilt offset in perpendicular direction (as position offset, not velocity)\r\n                this._position.x += perpX * tiltAmount;\r\n                this._position.y += perpY * tiltAmount;\r\n            }\r\n\r\n            this.node.setPosition(this._position);\r\n            this.checkVisibility();\r\n        }\r\n\r\n        if (this.isFacingMoveDir && this.speed > 0) {\r\n            const movementAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));\r\n            const finalAngle = movementAngle + this.defaultFacing;\r\n            this.node.setRotationFromEuler(0, 0, finalAngle);\r\n        }\r\n    }\r\n\r\n    public checkVisibility(): void {\r\n        // 这里目前的检查逻辑没有考虑旋转和缩放\r\n        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的\r\n        const visibleSize = BulletSystem.worldBounds;\r\n        this.node.getWorldPosition(this._position);\r\n        const isVisible = (this._position.x + this._selfSize.x) >= visibleSize.xMin &&\r\n            (this._position.x - this._selfSize.x) <= visibleSize.xMax &&\r\n            (this._position.y - this._selfSize.y) <= visibleSize.yMax &&\r\n            (this._position.y + this._selfSize.y) >= visibleSize.yMin;\r\n\r\n        // debug visibility\r\n        // if (!isVisible) {\r\n        //     console.log(\"Movable\", \"checkVisibility\", this.node.name + \" is not visible\");\r\n        //     console.log(\"Movable\", \"checkLeftBound  :\", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), \"<=\", visibleSize.xMax);\r\n        //     console.log(\"Movable\", \"checkRightBound :\", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), \">=\", visibleSize.xMin);\r\n        //     console.log(\"Movable\", \"checkTopBound   :\", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), \"<=\", visibleSize.yMax);\r\n        //     console.log(\"Movable\", \"checkBottomBound:\", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), \">=\", visibleSize.yMin);\r\n        // }\r\n\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this._isVisible === visible) return;\r\n\r\n        this._isVisible = visible;\r\n        if (visible) {\r\n            this.emit(eMoveEvent.onBecomeVisible);\r\n        } else {\r\n            this.emit(eMoveEvent.onBecomeInvisible);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the target to track\r\n     */\r\n    public setTarget(target: Node | null): void {\r\n        this.target = target;\r\n        this.isTrackingTarget = target !== null;\r\n    }\r\n\r\n    public setMovable(movable: boolean) {\r\n        this._isMovable = movable;\r\n\r\n        if (this._isMovable) {\r\n            // Initialize base position to current node position\r\n            this.node.getPosition(this._basePosition);\r\n        }\r\n    }\r\n}"]}