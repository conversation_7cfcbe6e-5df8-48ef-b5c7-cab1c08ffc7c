{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "CCFloat", "CCInteger", "Component", "WaveData", "eSpawnOrder", "eWaveCompletion", "GameIns", "ccclass", "property", "executeInEditMode", "WaveTrack", "WaveTrackGroup", "Wave", "type", "_isCompleted", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_nextSpawnIndex", "_spawnQueue", "isCompleted", "_reset", "length", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroup", "for<PERSON>ach", "group", "weight", "selfWeight", "trigger", "waveCompletion", "SpawnCount", "spawnCount", "count", "eval", "i", "randomWeight", "Math", "random", "push", "planeID", "tick", "dtInMiliseconds", "spawnFromQueue", "waveCompletionParam", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnPos", "spawnAngle", "spawnSpeed", "createPlane", "planeId", "pos", "angle", "speed", "enemy", "enemyManager", "addPlane", "console", "log", "setPos", "x", "y", "initMove", "delayDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvBC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CV,U;;2BAGpCW,S,WADZH,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACR,OAAD,C,UAERQ,QAAQ,CAACR,OAAD,C,UAERQ,QAAQ,CAACR,OAAD,C,2BARb,MACaU,SADb,CACuB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEP,C;;;;;;;iBAEG,C;;;;;;;iBAEK,C;;;;;;;iBAEF,C;;;;gCAITC,c,YADZJ,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACP,SAAD,C,WAERO,QAAQ,CAAC,CAACE,SAAD,CAAD,C,6BARb,MACaC,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,C;;;;;;;iBAEG,C;;;;;;;iBAEE,C;;;;;;;iBAEU,E;;;;sBAKpBC,I,aAFZL,OAAO,CAAC,MAAD,C,WACPE,iBAAiB,E,WAEbD,QAAQ,CAAC;AAACK,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,+CAHb,MAEaD,IAFb,SAE0BV,SAF1B,CAEoC;AAAA;AAAA;;AAAA;;AAIhC;AACJ;AACA;AANoC,eAOxBY,YAPwB,GAOA,KAPA;AAAA,eAUxBC,gBAVwB,GAUG,CAVH;AAAA,eAWxBC,cAXwB,GAWC,CAXD;AAAA,eAYxBC,YAZwB,GAYD,CAZC;AAahC;AAbgC,eAcxBC,eAdwB,GAcE,CAdF;AAAA,eAexBC,WAfwB,GAeA,EAfA;AAAA;;AAQhC;AACsB,YAAXC,WAAW,GAAG;AAAE,iBAAO,KAAKN,YAAZ;AAA2B;;AAQ9CO,QAAAA,MAAM,GAAG;AACb,eAAKP,YAAL,GAAoB,KAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKE,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,CAAiBG,MAAjB,GAA0B,CAA1B,CALa,CAMb;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAL,IAAiB,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA9D,EAAsE;AAClE,iBAAKT,YAAL,GAAoB,CAApB,CADkE,CAElE;;AACA,iBAAKO,QAAL,CAAcG,UAAd,CAAyBC,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKZ,YAAL,IAAqBY,KAAK,CAACC,MAA3B;AACAD,cAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKd,YAAxB;AACH,aAHD;AAIH;AACJ;;AAEDe,QAAAA,OAAO,GAAG;AACN,eAAKX,MAAL,GADM,CAGN;;;AACA,cAAI,KAAKG,QAAL,IAAiB,KAAKA,QAAL,CAAcS,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAAtE,EAAkF;AAC9E,gBAAMC,UAAU,GAAG,KAAKX,QAAL,CAAcY,KAAd,CAAoBC,IAApB,EAAnB;;AACA,gBAAI,KAAKb,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYC,MAA7C,EAAqD;AACjD,mBAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,UAApB,EAAgCG,CAAC,EAAjC,EAAqC;AACjC,oBAAMC,YAAY,GAAGC,IAAI,CAACC,MAAL,KAAgB,KAAKxB,YAA1C;;AACA,qBAAK,IAAMY,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,sBAAIY,YAAY,IAAIV,KAAK,CAACE,UAA1B,EAAsC;AAClC,yBAAKZ,WAAL,CAAiBuB,IAAjB,CAAsBb,KAAK,CAACc,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,aAVD,MAUO;AACH,mBAAK,IAAIL,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGH,UAApB,EAAgCG,EAAC,EAAjC,EAAqC;AACjC;AACA,qBAAKnB,WAAL,CAAiBuB,IAAjB,CAAsB,KAAKlB,QAAL,CAAcG,UAAd,CAAyBW,EAAC,GAAG,KAAKd,QAAL,CAAcG,UAAd,CAAyBL,MAAtD,EAA8DqB,OAApF;AACH;AACJ;AACJ;AACJ,SA5D+B,CA8DhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,KAAK/B,YAAT,EAAuB;AAEvB,eAAKC,gBAAL,IAAyB8B,eAAzB;;AACA,cAAI,KAAKrB,QAAL,CAAcS,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKnB,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,kBAAI,CAAC,KAAK8B,cAAL,EAAL,EAA4B;AACxB,qBAAKhC,YAAL,GAAoB,IAApB;AACH;AACJ;AACJ,WAPD,MAQK;AACD;AACA,gBAAI,KAAKC,gBAAL,IAAyB,KAAKS,QAAL,CAAcuB,mBAA3C,EAAgE;AAC5D,mBAAKjC,YAAL,GAAoB,IAApB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAKgC,cAAL;AACH;AACJ;AACJ;AACJ;;AAEOF,QAAAA,cAAc,GAAY;AAC9B,cAAI,KAAK5B,eAAL,IAAwB,KAAKC,WAAL,CAAiBG,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAK2B,oBAAL,CAA0B,KAAK/B,eAAL,EAA1B;AACA,eAAKF,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKS,QAAL,CAAc0B,aAAd,CAA4Bb,IAA5B,EAA9C;AACA,iBAAO,IAAP;AACH;;AAEOY,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAKhC,WAAL,CAAiBG,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAI8B,QAAQ,GAAG,KAAK5B,QAAL,CAAc4B,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAK7B,QAAL,CAAc6B,UAAd,CAAyBhB,IAAzB,EAAjB;AACA,cAAIiB,UAAU,GAAG,KAAK9B,QAAL,CAAc8B,UAAd,CAAyBjB,IAAzB,EAAjB;AAEA,eAAKkB,WAAL,CAAiB,KAAKpC,WAAL,CAAiBgC,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD,EAAgEC,UAAhE;AACH;;AAEON,QAAAA,cAAc,GAAS;AAC3B,cAAII,QAAQ,GAAG,KAAK5B,QAAL,CAAc4B,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAK7B,QAAL,CAAc6B,UAAd,CAAyBhB,IAAzB,EAAjB;AACA,cAAIiB,UAAU,GAAG,KAAK9B,QAAL,CAAc8B,UAAd,CAAyBjB,IAAzB,EAAjB;AAGH;;AAEakB,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4CC,KAA5C,EAA2D;AAAA;;AAAA;AAChF,gBAAIC,KAAK,SAAS;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,QAArB,CAA8BN,OAA9B,EAAuC,IAAvC,CAAlB;;AACA,gBAAII,KAAJ,EAAW;AACP;AACA;AACAG,cAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BR,OAA3B,EAAoCC,GAApC,EAAyCC,KAAzC,EAAgDC,KAAhD;AACAC,cAAAA,KAAK,CAACK,MAAN,CAAaR,GAAG,CAACS,CAAjB,EAAoBT,GAAG,CAACU,CAAxB;AACAP,cAAAA,KAAK,CAACQ,QAAN,CAAeT,KAAf,EAAsBD,KAAtB,EAA6B,KAAI,CAAClC,QAAL,CAAc6C,YAA3C;AACH;AAR+E;AASnF;;AA9H+B,O;;;;;iBAEF;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('WaveTrack')\r\nexport class WaveTrack {\r\n    @property(CCInteger)\r\n    public id = 0;\r\n    @property(CCFloat)\r\n    public speed = 0;\r\n    @property(CCFloat)\r\n    public accelerate = 0;\r\n    @property(CCFloat)\r\n    public Interval = 0;\r\n}\r\n\r\n@ccclass('WaveTrackGroup')\r\nexport class WaveTrackGroup {\r\n    @property(CCInteger)\r\n    public type = 0;\r\n    @property(CCInteger)\r\n    public loopNum = 0;\r\n    @property(CCInteger)\r\n    public formIndex = 0;\r\n    @property([WaveTrack])\r\n    public tracks: WaveTrack[] = [];\r\n}\r\n\r\n@ccclass('Wave')\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isCompleted: boolean = false;\r\n    // 当前波次是否已完成\r\n    public get isCompleted() { return this._isCompleted; }\r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 以下两个是用在waveCompletion == SpawnCount时的队列\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n\r\n    private _reset() {\r\n        this._isCompleted = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        // this._spawnQueue = this.waveData.planeList;\r\n    }\r\n\r\n    onLoad() {\r\n        if (this.waveData && this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            this._totalWeight = 0;\r\n            // add up _totalWeight if is random\r\n            this.waveData.spawnGroup.forEach((group) => {\r\n                this._totalWeight += group.weight;\r\n                group.selfWeight = this._totalWeight;\r\n            });\r\n        }\r\n    }\r\n\r\n    trigger() {\r\n        this._reset();\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData && this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            const spawnCount = this.waveData.count.eval();\r\n            if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                for (let i = 0; i < spawnCount; i++) {\r\n                    const randomWeight = Math.random() * this._totalWeight;\r\n                    for (const group of this.waveData.spawnGroup) {\r\n                        if (randomWeight <= group.selfWeight) {\r\n                            this._spawnQueue.push(group.planeID);\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n            } else {\r\n                for (let i = 0; i < spawnCount; i++) {\r\n                    // 通过取余实现循环\r\n                    this._spawnQueue.push(this.waveData.spawnGroup[i % this.waveData.spawnGroup.length].planeID);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (this._isCompleted) return;\r\n\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                if (!this.spawnFromQueue()) {\r\n                    this._isCompleted = true;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this.waveData.waveCompletionParam) {\r\n                this._isCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingleFromQueue(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n        return true;\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle, spawnSpeed);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        \r\n    }\r\n\r\n    private async createPlane(planeId: number, pos: Vec2, angle: number, speed: number) {\r\n        let enemy = await GameIns.enemyManager.addPlane(planeId, null);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            // enemy.setStandByTime(0);\r\n            console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            enemy.setPos(pos.x, pos.y);\r\n            enemy.initMove(speed, angle, this.waveData.delayDestroy);\r\n        }\r\n    }\r\n\r\n}"]}