import { _decorator, Component, Node } from 'cc';
import { Movable, eMoveEvent } from './Movable';

const { ccclass, property } = _decorator;

/**
 * 测试Movable事件系统的组件
 */
@ccclass('MovableEventTest')
export class MovableEventTest extends Component {

    @property(Node)
    movableNode: Node | null = null;

    private movable: Movable | null = null;
    private visibleListener: () => void;
    private invisibleListener: () => void;

    onLoad() {
        if (this.movableNode) {
            this.movable = this.movableNode.getComponent(Movable);
            if (this.movable) {
                this.setupEventListeners();
            }
        }
    }

    private setupEventListeners() {
        if (!this.movable) return;

        // 创建监听器函数
        this.visibleListener = () => {
            console.log('MovableEventTest: Object became visible');
        };

        this.invisibleListener = () => {
            console.log('MovableEventTest: Object became invisible');
        };

        // 添加事件监听器
        this.movable.on(eMoveEvent.onBecomeVisible, this.visibleListener);
        this.movable.on(eMoveEvent.onBecomeInvisible, this.invisibleListener);
    }

    onDestroy() {
        // 清理事件监听器
        if (this.movable) {
            this.movable.off(eMoveEvent.onBecomeVisible, this.visibleListener);
            this.movable.off(eMoveEvent.onBecomeInvisible, this.invisibleListener);
        }
    }

    /**
     * 测试手动触发可见性变化
     */
    public testVisibilityChange() {
        if (this.movable) {
            // 切换可见性状态进行测试
            this.movable.setVisible(!this.movable.isVisible);
        }
    }
}
