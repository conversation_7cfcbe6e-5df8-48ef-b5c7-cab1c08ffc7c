{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts"], "names": ["BulletSystem", "_decorator", "find", "path", "Rect", "EventGroup", "ObjectPool", "ccclass", "join", "init", "bounds", "worldBounds", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "bulletParentPath", "length", "foundNode", "console", "warn", "clearAll", "tick", "dt", "dtInMiliseconds", "tickEmitters", "tickBullets", "tickEventGroups", "i", "allEmitters", "emitter", "allBullets", "bullet", "allEventGroups", "group", "onCreateEmitter", "push", "onDestroyEmitter", "index", "indexOf", "splice", "onCreateBullet", "onCreate", "bulletConfig", "onDestroyBullet", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "createEmitterEventGroup", "ctx", "data", "eventGroups", "createBulletEventGroup", "eventGroup", "tryStart", "onCreateEventGroup", "onDestroyEventGroup", "destroy", "isEditor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "emitterEventGroupPath", "bulletEventGroupPath"], "mappings": ";;;qIAcaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAwCC,MAAAA,I,OAAAA,I;;AAG1DC,MAAAA,U,iBAAAA,U;;AAGAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcN,U;OACd;AAAEO,QAAAA;AAAF,O,GAAWL,I;AAEjB;AACA;AACA;AACA;;8BACaH,Y,GAAN,MAAMA,YAAN,CAAmB;AAgCJ,eAAJS,IAAI,CAACC,MAAD,EAAe;AAC7B,eAAKC,WAAL,GAAmBD,MAAnB;;AAEA,cAAI,CAAC,KAAKE,YAAN,IAAsB,CAAC,KAAKA,YAAL,CAAkBC,OAA7C,EAAsD;AAClD,gBAAI,KAAKC,gBAAL,CAAsBC,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,kBAAMC,SAAS,GAAGd,IAAI,CAAC,KAAKY,gBAAN,CAAtB;;AACA,kBAAIE,SAAJ,EAAe;AACX,qBAAKJ,YAAL,GAAoBI,SAApB;AACH,eAFD,MAGK;AACDC,gBAAAA,OAAO,CAACC,IAAR,CAAa,oBAAoB,KAAKJ,gBAAtC;AACH;AACJ;AACJ;;AAED;AAAA;AAAA,wCAAWK,QAAX;AACH;AAED;AACJ;AACA;;;AACsB,eAAJC,IAAI,CAACC,EAAD,EAAa;AAC3B,cAAMC,eAAe,GAAGD,EAAE,GAAG,IAA7B,CAD2B,CAG3B;;AACA,eAAKE,YAAL,CAAkBD,eAAlB,EAJ2B,CAK3B;AACA;;AAEA,eAAKE,WAAL,CAAiBF,eAAjB,EAR2B,CAS3B;AACA;;AAEA,eAAKG,eAAL,CAAqBH,eAArB,EAZ2B,CAa3B;AACH;;AAEyB,eAAZC,YAAY,CAACF,EAAD,EAAY;AAClC,eAAK,IAAIK,CAAC,GAAG,KAAKC,WAAL,CAAiBZ,MAAjB,GAA0B,CAAvC,EAA0CW,CAAC,IAAI,CAA/C,EAAkDA,CAAC,EAAnD,EAAuD;AACnD,gBAAME,OAAO,GAAG,KAAKD,WAAL,CAAiBD,CAAjB,CAAhB;AACAE,YAAAA,OAAO,CAACR,IAAR,CAAaC,EAAb;AACH;AACJ;;AAEwB,eAAXG,WAAW,CAACH,EAAD,EAAY;AACjC,eAAK,IAAIK,CAAC,GAAG,KAAKG,UAAL,CAAgBd,MAAhB,GAAyB,CAAtC,EAAyCW,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD,EAAsD;AAClD,gBAAMI,MAAM,GAAG,KAAKD,UAAL,CAAgBH,CAAhB,CAAf;AACAI,YAAAA,MAAM,CAACV,IAAP,CAAYC,EAAZ;AACH;AACJ;;AAE4B,eAAfI,eAAe,CAACJ,EAAD,EAAa;AACtC,eAAK,IAAIK,CAAC,GAAG,KAAKK,cAAL,CAAoBhB,MAApB,GAA6B,CAA1C,EAA6CW,CAAC,IAAI,CAAlD,EAAqDA,CAAC,EAAtD,EAA0D;AACtD,gBAAMM,KAAK,GAAG,KAAKD,cAAL,CAAoBL,CAApB,CAAd;AACAM,YAAAA,KAAK,CAACZ,IAAN,CAAWC,EAAX,EAFsD,CAGtD;AACA;AACA;AACA;AACH;AACJ;;AAE4B,eAAfY,eAAe,CAACL,OAAD,EAAkB;AAC3C,eAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,WAAL,CAAiBZ,MAArC,EAA6CW,CAAC,EAA9C,EAAkD;AAC9C,gBAAI,KAAKC,WAAL,CAAiBD,CAAjB,MAAwBE,OAA5B,EAAqC;AACjC;AACH;AACJ;;AAED,eAAKD,WAAL,CAAiBO,IAAjB,CAAsBN,OAAtB;AACH;;AAE6B,eAAhBO,gBAAgB,CAACP,OAAD,EAAkB;AAC5C,cAAMQ,KAAa,GAAG,KAAKT,WAAL,CAAiBU,OAAjB,CAAyBT,OAAzB,EAAkC,CAAlC,CAAtB;;AACA,cAAIQ,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZ,iBAAKT,WAAL,CAAiBW,MAAjB,CAAwBF,KAAxB,EAA+B,CAA/B;AACH;AACJ;;AAE2B,eAAdG,cAAc,CAACX,OAAD,EAAmBE,MAAnB,EAAmC;AAC3D;AACA;AACA;AACA;AACA;AACA;AAEAA,UAAAA,MAAM,CAACU,QAAP,CAAgBZ,OAAhB,EAAyBA,OAAO,CAACa,YAAjC;AACA,eAAKZ,UAAL,CAAgBK,IAAhB,CAAqBJ,MAArB;AACH;;AAE4B,eAAfY,eAAe,CAACZ,MAAD,EAAiB;AAC1CA,UAAAA,MAAM,CAACa,WAAP;AACA,cAAMP,KAAa,GAAG,KAAKP,UAAL,CAAgBQ,OAAhB,CAAwBP,MAAxB,EAAgC,CAAhC,CAAtB;;AACA,cAAIM,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZ,iBAAKP,UAAL,CAAgBS,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B;AACH;AACJ;;AAE8B,eAAjBQ,iBAAiB,GAAG;AAC9B,eAAK,IAAMd,MAAX,IAAqB,KAAKD,UAA1B,EAAsC;AAClCC,YAAAA,MAAM,CAACa,WAAP;AACH;;AACD,eAAKd,UAAL,GAAkB,EAAlB;AACH;;AAEoC,eAAvBgB,uBAAuB,CAACC,GAAD,EAAyBC,IAAzB,EAAqD;AAAA;;AACtF,0BAAAD,GAAG,CAAClB,OAAJ,0BAAaoB,WAAb,CAAyBd,IAAzB,CAA8B;AAAA;AAAA,wCAAeY,GAAf,EAAoBC,IAApB,CAA9B;AACH;;AAEmC,eAAtBE,sBAAsB,CAACH,GAAD,EAAyBC,IAAzB,EAAqD;AAAA;;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAMG,UAAU,GAAG;AAAA;AAAA,wCAAeJ,GAAf,EAAoBC,IAApB,CAAnB;AACAG,UAAAA,UAAU,CAACC,QAAX;AACA,yBAAAL,GAAG,CAAChB,MAAJ,yBAAYkB,WAAZ,CAAwBd,IAAxB,CAA6BgB,UAA7B;AACH;AAED;AACJ;AACA;;;AACoC,eAAlBE,kBAAkB,CAACF,UAAD,EAAyB;AACrD,eAAKnB,cAAL,CAAoBG,IAApB,CAAyBgB,UAAzB;AACH;;AAEgC,eAAnBG,mBAAmB,CAACH,UAAD,EAAyB;AACtD,cAAMd,KAAa,GAAG,KAAKL,cAAL,CAAoBM,OAApB,CAA4Ba,UAA5B,EAAwC,CAAxC,CAAtB;;AACA,cAAId,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZ,iBAAKL,cAAL,CAAoBO,MAApB,CAA2BF,KAA3B,EAAkC,CAAlC;AACH,WAJqD,CAKtD;;AACH;;AAEoB,eAAPkB,OAAO,CAACC,QAAD,EAA4B;AAAA,cAA3BA,QAA2B;AAA3BA,YAAAA,QAA2B,GAAP,KAAO;AAAA;;AAC7C,eAAKX,iBAAL;;AACA,cAAIW,QAAQ,IAAI,KAAK3C,YAArB,EAAmC;AAC/B,iBAAKA,YAAL,CAAkB4C,iBAAlB;AACH,WAJ4C,CAK7C;;;AACA,eAAK7B,WAAL,GAAmB,EAAnB;AACA,eAAKI,cAAL,GAAsB,EAAtB;AACA,eAAK0B,WAAL,GAAmB,IAAnB;AACA,eAAK7C,YAAL,GAAoB,IAApB;AACA;AAAA;AAAA,wCAAWO,QAAX;AACH;;AA3LqB,O;;AAAbnB,MAAAA,Y,CACcc,gB,GAA2B,oB;AADzCd,MAAAA,Y,CAEc0D,qB,GAAgC,6B;AAF9C1D,MAAAA,Y,CAGc2D,oB,GAA+B,4B;;AAEtD;AACJ;AACA;AAPa3D,MAAAA,Y,CAQK6B,U,GAAuB,E;;AAErC;AACJ;AACA;AAZa7B,MAAAA,Y,CAaK2B,W,GAAyB,E;;AAEvC;AACJ;AACA;AAjBa3B,MAAAA,Y,CAkBK+B,c,GAA+B,E;AAE7C;AACA;AArBS/B,MAAAA,Y,CAsBKY,Y,GAA0B,I;AAtB/BZ,MAAAA,Y,CAwBKyD,W,GAA8B,I;;AAE5C;AACJ;AACA;AACA;AA7BazD,MAAAA,Y,CA8BKW,W,GAAc,IAAIP,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,GAAf,EAAoB,IAApB,C", "sourcesContent": ["import { _decorator, find, path, Vec2, Node, resources, JsonAsset, Rect } from \"cc\";\nimport { Bullet } from \"./Bullet\";\nimport { Emitter } from \"./Emitter\";\nimport { EventGroup, EventGroupContext, eEventGroupStatus } from \"./EventGroup\";\nimport { EventGroupData } from \"../data/bullet/EventGroupData\";\nimport PlaneBase from \"db://assets/scripts/game/ui/plane/PlaneBase\";\nimport { ObjectPool } from \"./ObjectPool\";\nconst { ccclass } = _decorator;\nconst { join } = path;\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\nexport class BulletSystem {\n    public static readonly bulletParentPath: string = 'Canvas/bullet_root';\n    public static readonly emitterEventGroupPath: string = 'Game/emitter/events/Emitter';\n    public static readonly bulletEventGroupPath: string = 'Game/emitter/events/Bullet';\n\n    /**\n     * All active bullets\n     */\n    public static allBullets: Bullet[] = [];\n\n    /**\n     * All active emitters\n     */\n    public static allEmitters: Emitter[] = [];\n\n    /**\n     * All active action groups\n     */\n    public static allEventGroups: EventGroup[] = [];\n\n    // public static isEmitterEnabled: boolean = true;\n    // public static isBulletEnabled: boolean = true;\n    public static bulletParent: Node|null = null;\n\n    public static playerPlane: PlaneBase|null = null;\n\n    /**\n     * Bounds of the game world for bullet cleanup\n     * 这个值需要在合适的地方适当调整\n     */\n    public static worldBounds = new Rect(0, 0, 750, 1334);\n\n    public static init(bounds: Rect) {\n        this.worldBounds = bounds;\n\n        if (!this.bulletParent || !this.bulletParent.isValid) {\n            if (this.bulletParentPath.length > 0) {\n                const foundNode = find(this.bulletParentPath);\n                if (foundNode) {\n                    this.bulletParent = foundNode;\n                } \n                else {\n                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);\n                }\n            }\n        }\n\n        ObjectPool.clearAll();\n    }\n\n    /**\n     * Main update loop\n     */\n    public static tick(dt: number) {\n        const dtInMiliseconds = dt * 1000;\n\n        // const start_emitters = performance.now();\n        this.tickEmitters(dtInMiliseconds);\n        // const end_emitters = performance.now();\n        // console.log('tickEmitters cost: ', `${end_emitters - start_emitters}ms`)\n\n        this.tickBullets(dtInMiliseconds);\n        // const end_bullets = performance.now();\n        // console.log('tickBullets cost: ', `${end_bullets - end_emitters}ms`)\n        \n        this.tickEventGroups(dtInMiliseconds);\n        // console.log('tickEventGroups cost: ', `${performance.now() - end_bullets}ms`)\n    }\n\n    public static tickEmitters(dt:number) {\n        for (let i = this.allEmitters.length - 1; i >= 0; i--) {\n            const emitter = this.allEmitters[i];\n            emitter.tick(dt);\n        }\n    }\n\n    public static tickBullets(dt:number) {\n        for (let i = this.allBullets.length - 1; i >= 0; i--) {\n            const bullet = this.allBullets[i];\n            bullet.tick(dt);\n        }\n    }\n\n    public static tickEventGroups(dt: number) {\n        for (let i = this.allEventGroups.length - 1; i >= 0; i--) {\n            const group = this.allEventGroups[i];\n            group.tick(dt);\n            // group will remove itself when stopped\n            // if (group.status === eEventGroupStatus.Stopped) {\n            //     this.allEventGroups.splice(i, 1);\n            // }\n        }\n    }\n\n    public static onCreateEmitter(emitter:Emitter) {\n        for (let i = 0; i < this.allEmitters.length; i++) {\n            if (this.allEmitters[i] === emitter) {\n                return;\n            }\n        }\n\n        this.allEmitters.push(emitter);\n    }\n\n    public static onDestroyEmitter(emitter:Emitter) {\n        const index: number = this.allEmitters.indexOf(emitter, 0);\n        if (index > -1) {\n            this.allEmitters.splice(index, 1);\n        }\n    }\n\n    public static onCreateBullet(emitter: Emitter, bullet: Bullet) {\n        // 这个检查是否会比较冗余\n        // for (let i = 0; i < this.allBullets.length; i++) {\n        //     if (this.allBullets[i] === bullet) {\n        //         return;\n        //     }\n        // }\n\n        bullet.onCreate(emitter, emitter.bulletConfig);\n        this.allBullets.push(bullet);\n    }\n\n    public static onDestroyBullet(bullet: Bullet) {\n        bullet.willDestroy();\n        const index: number = this.allBullets.indexOf(bullet, 0);\n        if (index > -1) {\n            this.allBullets.splice(index, 1);\n        }\n    }\n\n    public static destroyAllBullets() {\n        for (const bullet of this.allBullets) {\n            bullet.willDestroy();\n        }\n        this.allBullets = [];\n    }\n\n    public static createEmitterEventGroup(ctx: EventGroupContext, data: EventGroupData): void {\n        ctx.emitter?.eventGroups.push(new EventGroup(ctx, data));\n    }\n\n    public static createBulletEventGroup(ctx: EventGroupContext, data: EventGroupData): void {\n        // the name is the json file name\n        // let finalPath = join(this.bulletEventGroupPath, name);\n        // resources.load(finalPath, JsonAsset, (err, data) => {\n        //     if (err) {\n        //         console.error(\"Failed to load bullet event group:\", err);\n        //         return null;\n        //     }\n        //     const eventData = EventGroupData.fromJSON(data.json);\n        //     const eventGroup = new EventGroup(ctx, eventData);\n        //     eventGroup.start();\n        //     ctx.bullet?.eventGroups.push(eventGroup);\n        // });\n        // console.log('createBulletEventGroup: ', data.name);\n        const eventGroup = new EventGroup(ctx, data);\n        eventGroup.tryStart();\n        ctx.bullet?.eventGroups.push(eventGroup);\n    }\n\n    /**\n     * Called when a new event group is created or turn active\n     */\n    public static onCreateEventGroup(eventGroup: EventGroup) {\n        this.allEventGroups.push(eventGroup);\n    }\n\n    public static onDestroyEventGroup(eventGroup: EventGroup) {\n        const index: number = this.allEventGroups.indexOf(eventGroup, 0);\n        if (index > -1) {\n            this.allEventGroups.splice(index, 1);\n        }\n        // console.log('onDestroyEventGroup: ', eventGroup.data.name);\n    }\n\n    public static destroy(isEditor: boolean = false) {\n        this.destroyAllBullets();\n        if (isEditor && this.bulletParent) {\n            this.bulletParent.removeAllChildren();\n        }\n        // 先直接这样清理掉\n        this.allEmitters = [];\n        this.allEventGroups = [];\n        this.playerPlane = null;\n        this.bulletParent = null;\n        ObjectPool.clearAll();\n    }\n}"]}