[{"__type__": "cc.Prefab", "_name": "Emitter_main_01", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Emitter_main_01", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": -438.999, "y": -131.586, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d36WBdI5xGP58zLtnCr2Vq"}, {"__type__": "2564dArcRFKZKoo3odCQrHw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "bulletID": 1000001, "bulletPrefab": {"__uuid__": "68ac1a9d-3829-40ab-9efb-62a7794c31ed", "__expectedType__": "cc.Prefab"}, "emitterData": {"__id__": 6}, "bulletData": {"__id__": 27}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9TmdPYGZEYYcUkBAgjFpq"}, {"__type__": "EmitterData", "isOnlyInScreen": true, "isPreWarm": false, "isLoop": true, "initialDelay": {"__id__": 7}, "preWarmDuration": {"__id__": 8}, "preWarmEffect": null, "emitDuration": {"__id__": 9}, "emitInterval": {"__id__": 10}, "emitPower": {"__id__": 11}, "loopInterval": {"__id__": 12}, "perEmitCount": {"__id__": 13}, "perEmitInterval": {"__id__": 14}, "perEmitOffsetX": {"__id__": 15}, "angle": {"__id__": 16}, "count": {"__id__": 17}, "arc": {"__id__": 18}, "radius": {"__id__": 19}, "emitEffect": null, "eventGroupData": [{"__id__": 20}]}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 60000, "isExpression": false, "expression": "60000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1000, "isExpression": false, "expression": "1000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 3, "isExpression": false, "expression": "3", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 200, "isExpression": false, "expression": "200", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 90, "isExpression": false, "expression": "90", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 3, "isExpression": false, "expression": "3", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 30, "isExpression": false, "expression": "30", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "EventGroupData", "name": "", "triggerCount": 1, "conditions": [{"__id__": 21}], "actions": [{"__id__": 23}]}, {"__type__": "EventConditionData", "op": 0, "type": 5, "compareOp": 4, "targetValue": {"__id__": 22}}, {"__type__": "ExpressionValue", "value": 3000, "isExpression": false, "expression": "3000", "serializedProgram": null}, {"__type__": "EventActionData", "type": 13, "duration": {"__id__": 24}, "targetValue": {"__id__": 25}, "targetValueType": 1, "transitionDuration": {"__id__": 26}, "wrapMode": 1, "easing": 2}, {"__type__": "ExpressionValue", "value": 10000, "isExpression": false, "expression": "10000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 90, "isExpression": false, "expression": "90", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "BulletData", "isFacingMoveDir": true, "isTrackingTarget": false, "isDestroyOutScreen": true, "isDestructive": false, "isDestructiveOnHit": false, "scale": {"__id__": 28}, "duration": {"__id__": 29}, "delayDestroy": {"__id__": 30}, "speed": {"__id__": 31}, "acceleration": {"__id__": 32}, "accelerationAngle": {"__id__": 33}, "eventGroupData": []}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 10000, "isExpression": false, "expression": "10000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1000, "isExpression": false, "expression": "1000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49VutYVl1GrbQe4mCj3Sww", "instance": null, "targetOverrides": null}]