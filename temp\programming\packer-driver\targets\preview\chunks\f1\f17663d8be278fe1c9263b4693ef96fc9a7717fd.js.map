{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts"], "names": ["_decorator", "Vec2", "Enum", "CCInteger", "ExpressionValue", "eCompareOp", "eConditionOp", "eTargetValueType", "eWrapMode", "eEasing", "ccclass", "property", "eSpawnOrder", "eWaveAngleType", "eWaveCompletion", "eWaveConditionType", "eWaveActionType", "WaveConditionData", "type", "displayName", "visible", "targetValueStr", "targetValue", "raw", "value", "And", "Player_Level", "Equal", "WaveActionData", "durationStr", "duration", "transitionDurationStr", "transitionDuration", "Speed", "Absolute", "Once", "Linear", "WaveEventGroupData", "SpawnGroup", "serializable", "WaveData", "tooltip", "planeAngleType", "Fixed", "_spawnPos", "countStr", "count", "spawnIntervalStr", "spawnInterval", "spawnPosXStr", "spawnPosX", "spawnPosYStr", "spawnPosY", "spawnPos", "set", "eval", "spawnAngleStr", "spawnAngle", "spawnSpeedStr", "spawnSpeed", "isFixedAngleType", "Random", "SpawnCount", "FacingMoveDir"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,e,iBAAAA,e;;AACqBC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,Y,iBAAAA,Y;AAAgCC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,S,iBAAAA,S;;AACnFC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;6BAElBY,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;;gCAKAC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;iCACYC,e,0BAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;eAAAA,e;;;oCAIAC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;;iCAMAC,e,0BAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;eAAAA,e;cAKZ;;;mCAEaC,iB,WADZP,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAEhB,IAAI;AAAA;AAAA,yCAAZ;AAA4BiB,QAAAA,WAAW,EAAE;AAAzC,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAGRT,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAEhB,IAAI;AAAA;AAAA,qCAAZ;AAA0BiB,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,UAIRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERT,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BAdb,MACaF,iBADb,CAC8D;AAAA;AAAA;;AAAA;;AAAA;;AAU1D;AAV0D;AAAA;;AAcjC,YAAdI,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACG,KAAD,EAAgB;AAAE,eAAKF,WAAL,CAAiBC,GAAjB,GAAuBC,KAAvB;AAA+B;;AAfhB,O;;;;;iBAEhC;AAAA;AAAA,4CAAaC,G;;;;;;;iBAGLV,kBAAkB,CAACW,Y;;;;;;;iBAGtB;AAAA;AAAA,wCAAWC,K;;;;;;;iBAIH;AAAA;AAAA,kDAAoB,GAApB,C;;;;gCAO9BC,c,YADZlB,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAIRT,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAAEQ,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRR,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAEhB,IAAI;AAAA;AAAA,iDAAZ;AAAgCiB,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAGRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAAEQ,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRR,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAEhB,IAAI;AAAA;AAAA,mCAAZ;AAAyBiB,QAAAA,WAAW,EAAE;AAAtC,OAAD,C,WAGRR,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAEhB,IAAI;AAAA;AAAA,+BAAZ;AAAuBiB,QAAAA,WAAW,EAAE;AAApC,OAAD,C,6BA9Bb,MACaS,cADb,CACwD;AAAA;AAAA;;AAIpD;AAJoD;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAQ9B,YAAXC,WAAW,GAAW;AAAE,iBAAO,KAAKC,QAAL,CAAcP,GAArB;AAA2B;;AACxC,YAAXM,WAAW,CAACL,KAAD,EAAgB;AAAE,eAAKM,QAAL,CAAcP,GAAd,GAAoBC,KAApB;AAA4B;;AAK3C,YAAdH,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACG,KAAD,EAAgB;AAAE,eAAKF,WAAL,CAAiBC,GAAjB,GAAuBC,KAAvB;AAA+B;;AAQ1C,YAArBO,qBAAqB,GAAW;AAAE,iBAAO,KAAKC,kBAAL,CAAwBR,KAA/B;AAAuC;;AACpD,YAArBO,qBAAqB,CAACP,KAAD,EAAgB;AAAE,eAAKQ,kBAAL,CAAwBR,KAAxB,GAAgCA,KAAhC;AAAwC;;AAxBtC,O;;;;;iBAErBR,eAAe,CAACiB,K;;;;;;;iBAIZ;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMG;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMK;AAAA;AAAA,oDAAiBC,Q;;;;;;;iBAGd;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMxB;AAAA;AAAA,sCAAUC,I;;;;;;;iBAGP;AAAA;AAAA,kCAAQC,M;;;;oCAIxBC,kB,aADZ3B,OAAO,CAAC,oBAAD,C,WAEHC,QAAQ,CAAC;AAAEQ,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRR,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAE,CAACD,iBAAD,CAAR;AAA6BE,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,WAGRR,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAE,CAACU,cAAD,CAAR;AAA0BT,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,8BARb,MACakB,kBADb,CACgC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEN,E;;;;;;;iBAGmB,E;;;;;;;iBAGN,E;;;;4BAI1BC,U,aADZ5B,OAAO,CAAC,YAAD,C,WAEHC,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEf,SAAP;AAAkBgB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRR,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEf,SAAP;AAAkBgB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC,KAAT;AAAgBmB,QAAAA,YAAY,EAAE;AAA9B,OAAD,C,gCARb,MACaD,UADb,CACwB;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEF,C;;;;;;;iBAGD,E;;;;;;;iBAGI,C;;;AAGzB;AACA;AACA;AACA;;;0BAEaE,Q,aADZ9B,OAAO,CAAC,UAAD,C,WAIHC,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAE,CAACoB,UAAD,CAAP;AAAqBnB,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,WAGRR,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEhB,IAAI,CAACU,WAAD,CAAX;AAA0BO,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,WAGRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRR,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEhB,IAAI,CAACY,eAAD,CAAX;AAA8BK,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,WAGRR,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEf,SAAP;AAAkBgB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAGRR,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAURR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRR,QAAQ,CAAC;AAACS,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERT,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRR,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEhB,IAAI,CAACW,cAAD,CAAX;AAA6BM,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,WAMRR,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEf,SAAP;AAAkBgB,QAAAA,WAAW,EAAE,MAA/B;AAAuCsB,QAAAA,OAAO,EAAE,mBAAhD;;AACNrB,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKsB,cAAL,IAAuB7B,cAAc,CAAC8B,KAA7C;AACH;;AAJK,OAAD,C,WAQRhC,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAEf,SAAP;AAAkBgB,QAAAA,WAAW,EAAE,QAA/B;AAAyCsB,QAAAA,OAAO,EAAE;AAAlD,OAAD,C,WAGR9B,QAAQ,CAAC;AAACO,QAAAA,IAAI,EAAE,CAACmB,kBAAD,CAAP;AAA6BlB,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,gCA1Eb,MACaqB,QADb,CACsB;AAAA;AAClB;AACA;AAFkB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAsCVI,SAtCU,GAsCQ,IAAI3C,IAAJ,EAtCR;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAYC,YAAR4C,QAAQ,GAAW;AAAE,iBAAO,KAAKC,KAAL,CAAWvB,GAAlB;AAAwB;;AACrC,YAARsB,QAAQ,CAACrB,KAAD,EAAgB;AAAE,eAAKsB,KAAL,CAAWvB,GAAX,GAAiBC,KAAjB;AAAyB;;AAWnC,YAAhBuB,gBAAgB,GAAW;AAAE,iBAAO,KAAKC,aAAL,CAAmBzB,GAA1B;AAAgC;;AAC7C,YAAhBwB,gBAAgB,CAACvB,KAAD,EAAgB;AAAE,eAAKwB,aAAL,CAAmBzB,GAAnB,GAAyBC,KAAzB;AAAiC;;AAOvD,YAAZyB,YAAY,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAe3B,GAAtB;AAA4B;;AACzC,YAAZ0B,YAAY,CAACzB,KAAD,EAAgB;AAAE,eAAK0B,SAAL,CAAe3B,GAAf,GAAqBC,KAArB;AAA6B;;AAE/C,YAAZ2B,YAAY,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAe7B,GAAtB;AAA4B;;AACzC,YAAZ4B,YAAY,CAAC3B,KAAD,EAAgB;AAAE,eAAK4B,SAAL,CAAe7B,GAAf,GAAqBC,KAArB;AAA6B;;AAGnD,YAAR6B,QAAQ,GAAS;AACxB,eAAKT,SAAL,CAAeU,GAAf,CAAmB,KAAKJ,SAAL,CAAeK,IAAf,EAAnB,EAA0C,KAAKH,SAAL,CAAeG,IAAf,EAA1C;;AACA,iBAAO,KAAKX,SAAZ;AACH;;AAKuB,YAAbY,aAAa,GAAW;AAAE,iBAAO,KAAKC,UAAL,CAAgBlC,GAAvB;AAA6B;;AAC1C,YAAbiC,aAAa,CAAChC,KAAD,EAAgB;AAAE,eAAKiC,UAAL,CAAgBlC,GAAhB,GAAsBC,KAAtB;AAA8B;;AAKhD,YAAbkC,aAAa,GAAW;AAAE,iBAAO,KAAKC,UAAL,CAAgBpC,GAAvB;AAA6B;;AAC1C,YAAbmC,aAAa,CAAClC,KAAD,EAAgB;AAAE,eAAKmC,UAAL,CAAgBpC,GAAhB,GAAsBC,KAAtB;AAA8B;;AAI7C,YAAhBoC,gBAAgB,GAAY;AACnC,iBAAO,KAAKlB,cAAL,IAAuB7B,cAAc,CAAC8B,KAA7C;AACH;;AA5DiB,O;;;;;iBAIgB,E;;;;;;;iBAGD/B,WAAW,CAACiD,M;;;;;;;iBAGZ;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMQ/C,eAAe,CAACgD,U;;;;;;;iBAGpB,E;;;;;;;iBAGG;AAAA;AAAA,kDAAoB,MAApB,C;;;;;;;iBAMH;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAEA;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAeA;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMA;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAMGjD,cAAc,CAACkD,a;;;;;;;iBAWtB,C;;;;;;;iBAGH,I;;;;;;;iBAGgB,E", "sourcesContent": ["\r\nimport { _decorator, error, v2, Vec2, <PERSON>fab, Enum, <PERSON><PERSON><PERSON>ger, CCFloat } from \"cc\";\r\nimport { ExpressionValue } from \"./bullet/ExpressionValue\";\r\nimport { IEventConditionData, eCompareOp, eConditionOp, IEventActionData, eTargetValueType, eWrapMode } from \"./bullet/EventGroupData\";\r\nimport { eEasing } from \"../bullet/Easing\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eSpawnOrder {\r\n    Sequential = 0,\r\n    Random = 1,\r\n}\r\n\r\nexport enum eWaveAngleType {\r\n    FacingMoveDir, FacingPlayer, Fixed, \r\n}\r\n\r\n// 波次完成条件\r\nexport enum eWaveCompletion {\r\n    Time, SpawnCount\r\n}\r\n\r\nexport enum eWaveConditionType {\r\n    Player_Level,       // 玩家等级\r\n\r\n    Spawn_Index,        // 当前生成的索引\r\n}\r\n\r\nexport enum eWaveActionType {\r\n    Speed,\r\n    SpeedAngle,\r\n}\r\n\r\n// 和发射器的事件组类似\r\n@ccclass(\"WaveConditionData\")\r\nexport class WaveConditionData implements IEventConditionData {\r\n    @property({ type: Enum(eConditionOp), displayName: '条件关系' })\r\n    public op: eConditionOp = eConditionOp.And;\r\n\r\n    @property({visible:false})\r\n    public type: eWaveConditionType = eWaveConditionType.Player_Level;\r\n\r\n    @property({ type: Enum(eCompareOp), displayName: '比较方式' })\r\n    public compareOp: eCompareOp = eCompareOp.Equal;\r\n    \r\n    // 条件值: 例如持续时间、距离\r\n    @property({visible:false})\r\n    public targetValue : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n}\r\n\r\n@ccclass(\"WaveActionData\")\r\nexport class WaveActionData implements IEventActionData {\r\n    @property({visible:false})\r\n    public type: eWaveActionType = eWaveActionType.Speed;\r\n    \r\n    // 持续时间: 0表示立即执行\r\n    @property({visible:false})\r\n    public duration: ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '持续时间' })\r\n    public get durationStr(): string { return this.duration.raw; }\r\n    public set durationStr(value: string) { this.duration.raw = value; }\r\n    \r\n    @property({visible:false})\r\n    public targetValue: ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n    \r\n    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })\r\n    public targetValueType: eTargetValueType = eTargetValueType.Absolute;\r\n    \r\n    @property({visible:false})\r\n    public transitionDuration : ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '变换到目标值所需时间' })\r\n    public get transitionDurationStr(): number { return this.transitionDuration.value; }\r\n    public set transitionDurationStr(value: number) { this.transitionDuration.value = value; }\r\n    \r\n    @property({ type: Enum(eWrapMode), displayName: '循环模式' })\r\n    wrapMode: eWrapMode = eWrapMode.Once;\r\n\r\n    @property({ type: Enum(eEasing), displayName: '缓动函数' })\r\n    public easing: eEasing = eEasing.Linear;\r\n}\r\n\r\n@ccclass(\"WaveEventGroupData\")\r\nexport class WaveEventGroupData {\r\n    @property({ displayName: '事件组名称' })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: [WaveConditionData], displayName: '条件列表' })\r\n    public conditions: WaveConditionData[] = [];\r\n\r\n    @property({ type: [WaveActionData], displayName: '行为列表' })\r\n    public actions: WaveActionData[] = [];\r\n}\r\n\r\n@ccclass(\"SpawnGroup\")\r\nexport class SpawnGroup {\r\n    @property({type: CCInteger, displayName: \"飞机ID\"})\r\n    planeID: number = 0;\r\n\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    weight: number = 50;\r\n\r\n    @property({visible:false, serializable: false})\r\n    selfWeight: number = 0;\r\n}\r\n\r\n/**\r\n * 波次数据：未来代替现有的EnemyWave\r\n * 所有时间相关的，单位都是毫秒(ms)\r\n */\r\n@ccclass(\"WaveData\")\r\nexport class WaveData {\r\n    // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发\r\n    // 因此这里不再配置触发条件\r\n    @property({type: [SpawnGroup], displayName: \"出生组\"})\r\n    public spawnGroup: SpawnGroup[] = [];\r\n\r\n    @property({type: Enum(eSpawnOrder), displayName: \"出生顺序\"})\r\n    public spawnOrder: eSpawnOrder = eSpawnOrder.Random;\r\n\r\n    @property({visible:false})\r\n    public count : ExpressionValue = new ExpressionValue('5');\r\n    @property({displayName: \"数量\"})\r\n    public get countStr(): string { return this.count.raw; }\r\n    public set countStr(value: string) { this.count.raw = value; }\r\n\r\n    @property({type: Enum(eWaveCompletion), displayName: \"波次完成条件\"})\r\n    public waveCompletion: eWaveCompletion = eWaveCompletion.SpawnCount;\r\n\r\n    @property({type: CCInteger, displayName: \"完成条件参数\"})\r\n    public waveCompletionParam: number = 10;\r\n\r\n    @property({visible:false})\r\n    public spawnInterval: ExpressionValue = new ExpressionValue('1000');\r\n    @property({displayName: \"出生间隔(ms)\"})\r\n    public get spawnIntervalStr(): string { return this.spawnInterval.raw; }\r\n    public set spawnIntervalStr(value: string) { this.spawnInterval.raw = value; }\r\n\r\n    @property({visible:false})\r\n    public spawnPosX : ExpressionValue = new ExpressionValue('0');\r\n    @property({visible:false})\r\n    public spawnPosY : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: \"出生位置X\"})\r\n    public get spawnPosXStr(): string { return this.spawnPosX.raw; }\r\n    public set spawnPosXStr(value: string) { this.spawnPosX.raw = value; }\r\n    @property({displayName: \"出生位置Y\"})\r\n    public get spawnPosYStr(): string { return this.spawnPosY.raw; }\r\n    public set spawnPosYStr(value: string) { this.spawnPosY.raw = value; }\r\n\r\n    private _spawnPos: Vec2 = new Vec2();\r\n    public get spawnPos(): Vec2 {\r\n        this._spawnPos.set(this.spawnPosX.eval(), this.spawnPosY.eval());\r\n        return this._spawnPos;\r\n    }\r\n\r\n    @property({visible:false})\r\n    public spawnAngle: ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: \"出生角度\"})\r\n    public get spawnAngleStr(): string { return this.spawnAngle.raw; }\r\n    public set spawnAngleStr(value: string) { this.spawnAngle.raw = value; }\r\n\r\n    @property({visible:false})\r\n    public spawnSpeed: ExpressionValue = new ExpressionValue('500');\r\n    @property({displayName: \"出生速度\"})\r\n    public get spawnSpeedStr(): string { return this.spawnSpeed.raw; }\r\n    public set spawnSpeedStr(value: string) { this.spawnSpeed.raw = value; }\r\n\r\n    @property({type: Enum(eWaveAngleType), displayName: \"单位朝向类型\"})\r\n    public planeAngleType: eWaveAngleType = eWaveAngleType.FacingMoveDir;\r\n    public get isFixedAngleType(): boolean {\r\n        return this.planeAngleType == eWaveAngleType.Fixed;\r\n    }\r\n\r\n    @property({type: CCInteger, displayName: \"单位朝向\", tooltip: '仅在单位朝向类型为Fixed时有效', \r\n        visible() { \r\n            //@ts-ignore\r\n            return this.planeAngleType == eWaveAngleType.Fixed;\r\n        }\r\n    })\r\n    public planeAngleFixed: number = 0;\r\n\r\n    @property({type: CCInteger, displayName: \"单位延迟销毁\", tooltip: '单位离开屏幕后, 延迟销毁的时间(ms), -1表示不销毁'})\r\n    public delayDestroy: number = 5000;\r\n\r\n    @property({type: [WaveEventGroupData], displayName: '事件组'})\r\n    public eventGroupData: WaveEventGroupData[] = [];\r\n}"]}