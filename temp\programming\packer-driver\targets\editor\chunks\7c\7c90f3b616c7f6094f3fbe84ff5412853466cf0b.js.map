{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/MovableEventTest.ts"], "names": ["_decorator", "Component", "Node", "Movable", "eMoveEvent", "ccclass", "property", "MovableEventTest", "movable", "visibleListener", "invisibleListener", "onLoad", "movableNode", "getComponent", "setupEventListeners", "console", "log", "on", "onBecomeVisible", "onBecomeInvisible", "onDestroy", "off", "testVisibilityChange", "setVisible", "isVisible"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACvBC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEZ;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;AAE9B;AACA;AACA;;kCAEaO,gB,WADZF,OAAO,CAAC,kBAAD,C,UAGHC,QAAQ,CAACJ,IAAD,C,2BAHb,MACaK,gBADb,SACsCN,SADtC,CACgD;AAAA;AAAA;;AAAA;;AAAA,eAKpCO,OALoC,GAKV,IALU;AAAA,eAMpCC,eANoC;AAAA,eAOpCC,iBAPoC;AAAA;;AAS5CC,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKJ,OAAL,GAAe,KAAKI,WAAL,CAAiBC,YAAjB;AAAA;AAAA,mCAAf;;AACA,gBAAI,KAAKL,OAAT,EAAkB;AACd,mBAAKM,mBAAL;AACH;AACJ;AACJ;;AAEOA,QAAAA,mBAAmB,GAAG;AAC1B,cAAI,CAAC,KAAKN,OAAV,EAAmB,OADO,CAG1B;;AACA,eAAKC,eAAL,GAAuB,MAAM;AACzBM,YAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ;AACH,WAFD;;AAIA,eAAKN,iBAAL,GAAyB,MAAM;AAC3BK,YAAAA,OAAO,CAACC,GAAR,CAAY,2CAAZ;AACH,WAFD,CAR0B,CAY1B;;;AACA,eAAKR,OAAL,CAAaS,EAAb,CAAgB;AAAA;AAAA,wCAAWC,eAA3B,EAA4C,KAAKT,eAAjD;AACA,eAAKD,OAAL,CAAaS,EAAb,CAAgB;AAAA;AAAA,wCAAWE,iBAA3B,EAA8C,KAAKT,iBAAnD;AACH;;AAEDU,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAKZ,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaa,GAAb,CAAiB;AAAA;AAAA,0CAAWH,eAA5B,EAA6C,KAAKT,eAAlD;AACA,iBAAKD,OAAL,CAAaa,GAAb,CAAiB;AAAA;AAAA,0CAAWF,iBAA5B,EAA+C,KAAKT,iBAApD;AACH;AACJ;AAED;AACJ;AACA;;;AACWY,QAAAA,oBAAoB,GAAG;AAC1B,cAAI,KAAKd,OAAT,EAAkB;AACd;AACA,iBAAKA,OAAL,CAAae,UAAb,CAAwB,CAAC,KAAKf,OAAL,CAAagB,SAAtC;AACH;AACJ;;AAnD2C,O;;;;;iBAGjB,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\nimport { Movable, eMoveEvent } from './Movable';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 测试Movable事件系统的组件\n */\n@ccclass('MovableEventTest')\nexport class MovableEventTest extends Component {\n\n    @property(Node)\n    movableNode: Node | null = null;\n\n    private movable: Movable | null = null;\n    private visibleListener: () => void;\n    private invisibleListener: () => void;\n\n    onLoad() {\n        if (this.movableNode) {\n            this.movable = this.movableNode.getComponent(Movable);\n            if (this.movable) {\n                this.setupEventListeners();\n            }\n        }\n    }\n\n    private setupEventListeners() {\n        if (!this.movable) return;\n\n        // 创建监听器函数\n        this.visibleListener = () => {\n            console.log('MovableEventTest: Object became visible');\n        };\n\n        this.invisibleListener = () => {\n            console.log('MovableEventTest: Object became invisible');\n        };\n\n        // 添加事件监听器\n        this.movable.on(eMoveEvent.onBecomeVisible, this.visibleListener);\n        this.movable.on(eMoveEvent.onBecomeInvisible, this.invisibleListener);\n    }\n\n    onDestroy() {\n        // 清理事件监听器\n        if (this.movable) {\n            this.movable.off(eMoveEvent.onBecomeVisible, this.visibleListener);\n            this.movable.off(eMoveEvent.onBecomeInvisible, this.invisibleListener);\n        }\n    }\n\n    /**\n     * 测试手动触发可见性变化\n     */\n    public testVisibilityChange() {\n        if (this.movable) {\n            // 切换可见性状态进行测试\n            this.movable.setVisible(!this.movable.isVisible);\n        }\n    }\n}\n"]}