import { _decorator} from 'cc';
import { GameIns } from '../../../GameIns';
import { GameEnum } from '../../../const/GameEnum';
import PlaneBase from '../PlaneBase';
import FCollider from '../../../collider-system/FCollider';
import { Bullet } from '../../../bullet/Bullet';
import { Plane } from 'db://assets/bundles/common/script/ui/Plane';
import { EnemyData } from '../../../data/EnemyData';
import { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';
import TrackComponent from '../../base/TrackComponent';
import { Movable, eMoveEvent } from '../../../move/Movable';

const { ccclass, property } = _decorator;

@ccclass('EnemyPlaneBase')
export default class EnemyPlaneBase extends PlaneBase {
    @property(Plane)//敌机显示组件
    plane: Plane | null = null;
    _enemyData: EnemyData | null = null;
    _trackCom: TrackComponent | null = null;
    _moveCom: Movable | null = null;
    public get moveCom() { return this._moveCom; }

    removeAble:boolean = false;
    bullets: Bullet[] = [];

    initPlane(data: EnemyData,trackData:any = null) {
        this.enemy = true
        this._enemyData = data;
        super.init();
        this.refreshProperty();
        if (trackData){
            this.initTrack(trackData);
        }
    }

    initTrack(trackData:any) {
        this._trackCom!.init(this, trackData.trackData, trackData.trackParams, trackData.offset);

        this._trackCom!.setTrackOverCall(() => {
            this.toDie(GameEnum.EnemyDestroyType.TrackOver);
        });

        this._trackCom!.setTrackLeaveCall(() => {
            this.toDie(GameEnum.EnemyDestroyType.Leave);
        });
    }

    initMove(speed: number, angle: number, delayDestroy: number = 0) {
        this._moveCom!.speed = speed;
        this._moveCom!.speedAngle = angle;
        this._moveCom!.setMovable(true);

        this._moveCom!.on(eMoveEvent.onBecomeInvisible, () => {
            this.scheduleOnce(this._dieWhenOffScreen, delayDestroy);
        });

        this._moveCom!.on(eMoveEvent.onBecomeVisible, () => {
            this.unschedule(this._dieWhenOffScreen);
        });
    }

    _dieWhenOffScreen() {
        this.toDie(GameEnum.EnemyDestroyType.Leave);
    }

    refreshProperty() {
        this.curHp = this._enemyData?.getFinalAttributeByKey(AttributeConst.MaxHP)!;
        this.maxHp = this.curHp;
    }

    getAttack():number {
        return this._enemyData?.getFinalAttributeByKey(AttributeConst.Attack) || 0;
    }

    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean  {
        if (!super.toDie(destroyType)) {
            return false;
        }
        this.colliderEnabled = false;

        this.onDie(destroyType);
        return true;
    }

    onDie(destroyType: number) {
        this.willRemove();

        switch (destroyType) {
            case GameEnum.EnemyDestroyType.Die:
                this.playDieAnim(()=>{
                    this.removeAble = true;
                });
                break;

            case GameEnum.EnemyDestroyType.Leave:
            case GameEnum.EnemyDestroyType.TrackOver:
            case GameEnum.EnemyDestroyType.TimeOver:
                break;
        }
    }

    playDieAnim(callBack: Function) {
        // if (this.plane) {
        //     this.plane.playDieAnim(callBack);   
        // }
        callBack?.();
    }

    onCollide(collider: FCollider) {
        if (!this.isDead) {
            if (collider.entity instanceof Bullet) {
                const attack = collider.entity.getAttack();
                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), attack);
                this.hurt(attack)
            }
        }
    }

    /**
     * 准备移除敌机
     */
    willRemove() {

    }

    addBullet(bullet: Bullet) {
        if (this.bullets) {
            this.bullets.push(bullet);
        }
    }

    /**
     * 从敌人移除子弹
     * @param {Bullet} bullet 子弹对象
     */
    removeBullet(bullet: Bullet) {
        if (this.bullets) {
            const index = this.bullets.indexOf(bullet);
            if (index >= 0) {
                this.bullets.splice(index, 1);
            }
        }
    }

    setPos(x: number, y: number) {
        this.node.setPosition(x, y);
    }
}