System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Property, PropertyContainer, PropertyContainerComponent, _crd, ccclass, property;

  _export({
    Property: void 0,
    PropertyContainer: void 0,
    PropertyContainerComponent: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "602fbBzMT5A4KkyTPgLZxiE", "PropertyContainer", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass,
        property
      } = _decorator); // Expression wrapper

      _export("Property", Property = class Property {
        constructor(value, onModified) {
          if (onModified === void 0) {
            onModified = null;
          }

          this._value = void 0;
          this._isDirty = false;
          // listeners
          this._listeners = [];
          this._onModified = null;
          this._value = value;
          this._isDirty = false;
          this._onModified = onModified;
        }

        get isDirty() {
          return this._isDirty;
        }

        setDirty(value) {
          this._isDirty = value;
        }

        get value() {
          return this._value;
        }

        set value(newValue) {
          if (this._value === newValue) return;
          this._value = newValue;

          if (this._onModified) {
            this._onModified(newValue);
          }

          this.setDirty(true);
        }

        on(listener) {
          this._listeners.push(listener);
        }

        off(listener) {
          this._listeners = this._listeners.filter(l => l !== listener);
        }

        notify(force) {
          if (force === void 0) {
            force = false;
          }

          if (force || this.isDirty) {
            this._listeners.forEach(listener => listener(this._value));

            this.setDirty(false);
          }
        }

        clear() {
          this._listeners.length = 0;
        }

      });

      _export("PropertyContainer", PropertyContainer = class PropertyContainer {
        constructor() {
          this._anyPropertyDirty = false;
          this._properties = new Map();
        }

        get anyPropertyDirty() {
          return this._anyPropertyDirty;
        }

        addProperty(key, value) {
          var property = this._properties.get(key);

          if (property) {
            property.value = value;
          } else {
            property = new Property(value, val => {
              this._anyPropertyDirty = true;
            });

            this._properties.set(key, property);
          }

          return property;
        }

        removeProperty(key) {
          this._properties.delete(key);
        }

        getProperty(key) {
          return this._properties.get(key);
        }

        getPropertyValue(key) {
          // get property as PropertyValue<T>
          var property = this._properties.get(key);

          return property == null ? void 0 : property.value;
        }

        setProperty(key, value) {
          var property = this._properties.get(key);

          if (property) {
            property.value = value;
          }
        }

        forEachProperty(callback) {
          this._properties.forEach((property, key) => callback(key, property));
        }

        notifyAll(force) {
          if (force === void 0) {
            force = false;
          }

          this._properties.forEach(property => property.notify(force));

          this._anyPropertyDirty = false;
        }

        clear() {
          this._properties.clear();
        }

      }); // Use this one to simplify class hierarchy


      _export("PropertyContainerComponent", PropertyContainerComponent = class PropertyContainerComponent extends Component {
        constructor() {
          super(...arguments);
          this._propertyContainer = new PropertyContainer();
        }

        get anyPropertyDirty() {
          return this._propertyContainer.anyPropertyDirty;
        }

        addProperty(key, value) {
          return this._propertyContainer.addProperty(key, value);
        }

        removeProperty(key) {
          this._propertyContainer.removeProperty(key);
        }

        getProperty(key) {
          return this._propertyContainer.getProperty(key);
        }

        getPropertyValue(key) {
          return this._propertyContainer.getPropertyValue(key);
        }

        setProperty(key, value) {
          this._propertyContainer.setProperty(key, value);
        }

        notifyAll(force) {
          if (force === void 0) {
            force = false;
          }

          this._propertyContainer.notifyAll(force);
        }

        clear() {
          this._propertyContainer.clear();
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=85f912b58c5ed6f441cd1c367fad16fd1938d224.js.map